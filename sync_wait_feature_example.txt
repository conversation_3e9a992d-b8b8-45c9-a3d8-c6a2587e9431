新增功能：Sync/Wait指令处理

## 功能描述
在tile_instruction_decoder.sv和itrace.sv中新增了对twait和ace_sync类指令的特殊处理。
这些指令在fetch阶段之后会被直接丢弃，不会进入后续的dispatch、execute等pipeline阶段。

## 修改内容

### 1. tile_instruction_decoder.sv 新增函数
```systemverilog
// Function to check if instruction is a twait or ace_sync instruction that should be discarded after fetch
function automatic logic is_sync_or_wait_instruction(input logic [31:0] first_word);
```

该函数检测以下指令类型：
- tuop_101 (3'b101): 所有sync操作，包括：
  - twait (ctrluop=3'b001, waitop=3'b111)
  - twait.mem (ctrluop=3'b001, waitop=3'b111, bit[26]=1)
  - 其他sync操作
  
- tuop_111 (3'b111): ACE sync操作，包括：
  - ace_bsync (ace_misc_en=1, miscop=3'b000)
  - ace_nbsync (ace_misc_en=1, miscop=3'b010)

### 2. itrace.sv 新增处理逻辑

#### 新增信号检测：
```systemverilog
// Sync/Wait指令检测assign语句
logic ch0_is_sync_wait, ch1_is_sync_wait;
assign ch0_is_sync_wait = ch0_active && is_sync_or_wait_instruction(ifu_i0_instr);
assign ch1_is_sync_wait = ch1_active && is_sync_or_wait_instruction(ifu_i1_instr);
```

#### 新增处理逻辑：
在tile指令收集逻辑之前，添加了sync/wait指令的特殊处理：
1. 检测到sync/wait指令时，立即创建指令记录并写入itrace.log文件
2. 记录包含FetchStartTime和DecodeStartTime，后续pipeline时间设为0
3. 从fetch请求队列中移除对应的取指请求
4. 在debug模式下，输出详细信息，包括反编译字符串
5. 这些指令不会被添加到指令队列中，也不会进入后续的pipeline阶段

## 示例输出

### itrace.log文件示例：
```
InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb0012ab,"twait",1025,1030,0,0,0
3,0x000000001010,0x00000000fb801000,"ace_bsync x0",1035,1040,0,0,0
4,0x000000001014,0x00000000fb234567,"tmma.ttt t3, t1, t2",1045,1050,1055,1060,1065
```

### Debug日志示例：
```
Time: 1030 - CH0 Sync/Wait Written to Log: PC=0x000000001008, Instr=0xfb0012ab, Disasm="twait", FetchTime=1025.00
Time: 1040 - CH1 Sync/Wait Written to Log: PC=0x000000001010, Instr=0xfb801000, Disasm="ace_bsync x0", FetchTime=1035.00
```

### 支持的指令示例：
- twait: 0xfb0012ab
- twait.mem: 0xfb4012ab  
- ace_bsync: 0xfb801000
- ace_nbsync: 0xfb802000

## 工作流程
1. 指令fetch完成后，检查是否为sync/wait指令
2. 如果是sync/wait指令：
   - 立即创建指令记录，包含反编译字符串
   - 设置FetchStartTime和DecodeStartTime
   - 将DispatchStartTime、ExecuteStartTime、ExecuteEndTime设为0
   - 直接写入itrace.log文件
   - 从fetch请求队列中移除对应的取指请求
   - 输出debug信息（如果启用）
   - 不进入指令队列，直接丢弃
3. 如果不是sync/wait指令：
   - 按原有逻辑处理tile指令收集

## 优势
- 在itrace.log文件中可以看到sync/wait指令的完整信息
- 显示这些指令的fetch和decode时间，便于性能分析
- 避免sync/wait指令在pipeline中造成阻塞
- 正确模拟硬件行为，这些指令在中间阶段被消耗
- 提供详细的debug信息用于分析
- 保持与其他tile指令处理逻辑的一致性
- 通过时间戳为0可以清楚识别哪些是sync/wait指令
