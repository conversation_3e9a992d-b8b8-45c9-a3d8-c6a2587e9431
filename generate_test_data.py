#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Generate test data for ISQ monitor visualization
Creates a sample isq_monitor.log file for testing
"""

import csv
import math
import random

def generate_test_data(filename='test_isq_monitor.log', num_points=500):
    """Generate test ISQ monitor data"""
    
    print(f"Generating test data: {filename}")
    print(f"Number of data points: {num_points}")
    
    with open(filename, 'w', newline='') as csvfile:
        writer = csv.writer(csvfile)
        
        # Write header
        writer.writerow(['Time', 'CycleCount', 'CISQ_VLD_CNT', 'MISQ_VLD_CNT', 
                        'CISQ_Utilization', 'MISQ_Utilization'])
        
        # Generate data points
        for i in range(num_points):
            time = (i + 1) * 10  # Every 10 time units
            cycle_count = (i + 1) * 10  # Every 10 cycles
            
            # Generate realistic utilization patterns
            # CISQ: sine wave with noise and trend
            cisq_base = 50 + 30 * math.sin(i * 0.02) + 10 * math.sin(i * 0.1)
            cisq_noise = random.uniform(-5, 5)
            cisq_util = max(0, min(100, cisq_base + cisq_noise))
            
            # MISQ: different pattern, generally lower
            misq_base = 40 + 25 * math.sin(i * 0.015 + 1) + 8 * math.sin(i * 0.08)
            misq_noise = random.uniform(-4, 4)
            misq_util = max(0, min(100, misq_base + misq_noise))
            
            # Calculate counts from utilization (assuming max count = 32)
            cisq_count = int(cisq_util * 32 / 100)
            misq_count = int(misq_util * 32 / 100)
            
            # Add some spikes to make it interesting
            if i % 50 == 0:  # Every 50 points, add a spike
                cisq_util = min(100, cisq_util + random.uniform(10, 30))
                cisq_count = int(cisq_util * 32 / 100)
            
            if i % 73 == 0:  # Different interval for MISQ
                misq_util = min(100, misq_util + random.uniform(15, 25))
                misq_count = int(misq_util * 32 / 100)
            
            writer.writerow([time, cycle_count, cisq_count, misq_count, 
                           f"{cisq_util:.2f}", f"{misq_util:.2f}"])
    
    print(f"Test data generated successfully: {filename}")
    print("You can now test the visualization scripts with this data")

if __name__ == "__main__":
    generate_test_data()
    
    # Also generate a smaller file for quick testing
    generate_test_data('small_test.log', 100)
