#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Quick ISQ Utilization Visualization Script
Simple and easy-to-use version for quick ISQ utilization trend viewing

Usage:
python quick_plot.py
"""

import pandas as pd
import matplotlib.pyplot as plt
import os

def plot_isq_utilization(filename='isq_monitor.log'):
    """
    Read ISQ monitor file and plot utilization charts

    Args:
        filename: ISQ monitor output filename
    """

    # Check if file exists
    if not os.path.exists(filename):
        print(f"Error: Cannot find file {filename}")
        print("Please ensure ISQ monitor is running and generating output file")
        return

    try:
        # Read CSV file
        print(f"Reading file: {filename}")
        df = pd.read_csv(filename)

        # Check data
        if df.empty:
            print("Warning: File is empty")
            return

        print(f"Read {len(df)} data records")
        
        # 创建图表
        plt.figure(figsize=(14, 8))
        
        # Plot CISQ utilization
        plt.subplot(2, 1, 1)
        plt.plot(df['Time'], df['CISQ_Utilization'], 'b-', linewidth=2,
                marker='o', markersize=2, label='CISQ Utilization')
        plt.ylabel('CISQ Utilization (%)', fontsize=12)
        plt.title('ISQ Utilization Over Simulation Time', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.ylim(0, 100)
        plt.legend()

        # Add CISQ statistics
        cisq_avg = df['CISQ_Utilization'].mean()
        cisq_max = df['CISQ_Utilization'].max()
        cisq_min = df['CISQ_Utilization'].min()
        plt.text(0.02, 0.95, f'Avg: {cisq_avg:.1f}%\nMax: {cisq_max:.1f}%\nMin: {cisq_min:.1f}%',
                transform=plt.gca().transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

        # Plot MISQ utilization
        plt.subplot(2, 1, 2)
        plt.plot(df['Time'], df['MISQ_Utilization'], 'r-', linewidth=2,
                marker='s', markersize=2, label='MISQ Utilization')
        plt.xlabel('Simulation Time', fontsize=12)
        plt.ylabel('MISQ Utilization (%)', fontsize=12)
        plt.grid(True, alpha=0.3)
        plt.ylim(0, 100)
        plt.legend()

        # Add MISQ statistics
        misq_avg = df['MISQ_Utilization'].mean()
        misq_max = df['MISQ_Utilization'].max()
        misq_min = df['MISQ_Utilization'].min()
        plt.text(0.02, 0.95, f'Avg: {misq_avg:.1f}%\nMax: {misq_max:.1f}%\nMin: {misq_min:.1f}%',
                transform=plt.gca().transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
        
        plt.tight_layout()
        
        # Display overall statistics
        print("\n=== ISQ Utilization Statistics ===")
        print(f"Simulation time range: {df['Time'].min()} - {df['Time'].max()}")
        print(f"Total cycles: {df['CycleCount'].max():,}")
        print(f"Sample points: {len(df):,}")
        print(f"\nCISQ Utilization:")
        print(f"  Average: {cisq_avg:.2f}%")
        print(f"  Maximum: {cisq_max:.2f}%")
        print(f"  Minimum: {cisq_min:.2f}%")
        print(f"\nMISQ Utilization:")
        print(f"  Average: {misq_avg:.2f}%")
        print(f"  Maximum: {misq_max:.2f}%")
        print(f"  Minimum: {misq_min:.2f}%")

        # Check for queue full conditions
        cisq_full_count = (df['CISQ_Utilization'] >= 100).sum()
        misq_full_count = (df['MISQ_Utilization'] >= 100).sum()

        if cisq_full_count > 0:
            print(f"\nWarning: CISQ queue full count: {cisq_full_count}")
        if misq_full_count > 0:
            print(f"Warning: MISQ queue full count: {misq_full_count}")
            
        plt.show()
        
    except Exception as e:
        print(f"Error processing file: {e}")

def plot_combined_utilization(filename='isq_monitor.log'):
    """
    Plot CISQ and MISQ utilization on the same chart

    Args:
        filename: ISQ monitor output filename
    """

    if not os.path.exists(filename):
        print(f"Error: Cannot find file {filename}")
        return

    try:
        df = pd.read_csv(filename)

        if df.empty:
            print("Warning: File is empty")
            return
        
        # Create single chart
        plt.figure(figsize=(12, 6))

        # Plot two utilization curves
        plt.plot(df['Time'], df['CISQ_Utilization'], 'b-', linewidth=2,
                marker='o', markersize=2, label='CISQ Utilization', alpha=0.8)
        plt.plot(df['Time'], df['MISQ_Utilization'], 'r-', linewidth=2,
                marker='s', markersize=2, label='MISQ Utilization', alpha=0.8)

        # Set chart properties
        plt.xlabel('Simulation Time', fontsize=12)
        plt.ylabel('Utilization (%)', fontsize=12)
        plt.title('ISQ Utilization Comparison Over Time', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.ylim(0, 100)
        plt.legend(fontsize=11)

        # Add horizontal reference lines
        plt.axhline(y=50, color='gray', linestyle='--', alpha=0.5, label='50% Reference')
        plt.axhline(y=80, color='orange', linestyle='--', alpha=0.5, label='80% Reference')
        plt.axhline(y=100, color='red', linestyle='--', alpha=0.5, label='100% (Full)')

        # Update legend
        plt.legend(fontsize=10)

        plt.tight_layout()
        plt.show()

    except Exception as e:
        print(f"Error processing file: {e}")

if __name__ == "__main__":
    print("ISQ Utilization Quick Visualization Tool")
    print("=" * 40)

    # Check default file
    filename = 'isq_monitor.log'

    if os.path.exists(filename):
        print("Choose display mode:")
        print("1. Separate display (Recommended)")
        print("2. Combined display")

        choice = input("Please choose (1/2, default 1): ").strip()

        if choice == '2':
            plot_combined_utilization(filename)
        else:
            plot_isq_utilization(filename)
    else:
        print(f"Default file not found: {filename}")
        custom_file = input("Please enter ISQ monitor file path: ").strip()
        if custom_file:
            plot_isq_utilization(custom_file)
