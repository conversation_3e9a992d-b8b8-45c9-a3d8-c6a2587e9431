# itrace_monitor 模块使用指南

## 概述

`itrace_monitor` 模块是一个重构后的NPU性能监控模块，原本是一个包含文件（include file），现在改为可实例化的SystemVerilog模块。这样的设计避免了变量冲突问题，使得代码更加模块化和可重用。

## 主要特性

1. **模块化设计**：从include文件重构为独立模块
2. **Backdoor信号连接**：通过层次化路径直接访问顶层信号，无需在模块接口声明
3. **避免变量冲突**：每个实例都有独立的变量空间
4. **完整的性能监控**：支持指令跟踪、断流统计、功能单元利用率统计

## 模块接口

```systemverilog
module itrace_monitor (
    input logic clk,        // 时钟信号
    input logic pe_resetn   // 复位信号（低有效）
);
```

## 使用方法

### 1. 在testbench中实例化

```systemverilog
module npu_tb;
    logic clk;
    logic pe_resetn;
    
    // 实例化 itrace_monitor 模块
    itrace_monitor u_itrace_monitor (
        .clk(clk),
        .pe_resetn(pe_resetn)
    );
    
    // 其他testbench逻辑...
endmodule
```

### 2. 多实例支持

由于模块化设计，可以同时实例化多个监控器：

```systemverilog
// 为不同的PE核心创建独立的监控器
itrace_monitor u_itrace_monitor_pe0 (
    .clk(clk),
    .pe_resetn(pe_resetn)
);

itrace_monitor u_itrace_monitor_pe1 (
    .clk(clk),
    .pe_resetn(pe_resetn)
);
```

### 3. 编译选项

确保在编译时包含必要的package：

```bash
# 编译时需要包含 tile_decoder_pkg
vlog +incdir+<path_to_packages> tile_decoder_pkg.sv itrace.sv
```

## 信号连接

模块通过backdoor方式直接连接到顶层信号，主要包括：

- **取指信号**：`PE_TOP.u_ax45mpv_cluster_wrapper...kv_ifu.*`
- **dispatch信号**：`PE_TOP.u_tcore.tc_tfe.isq.*`
- **执行单元信号**：`PE_TOP.u_tcore.tc_tfe.*`

## 输出文件

模块会生成以下文件：

1. **itrace.log**：主要的性能跟踪日志
2. **itrace_debug.log**：调试信息（当启用DEBUG时）
3. **incomplete_instructions.log**：未完成指令的详细信息

## 调试选项

通过仿真参数启用调试模式：

```bash
# 启用调试输出
vsim +ITRACE_DEBUG_ENABLE <testbench>
```

## 注意事项

1. **层次化路径依赖**：模块依赖特定的设计层次结构，如果设计层次发生变化，需要相应更新信号路径
2. **时钟域**：确保提供的时钟信号与被监控的设计使用相同的时钟域
3. **复位时序**：复位信号应与被监控设计的复位保持同步

## 优势

相比原来的include文件方式：

1. **避免变量冲突**：每个实例有独立的变量空间
2. **更好的封装性**：模块内部实现对外部不可见
3. **支持多实例**：可以同时监控多个PE核心
4. **更清晰的接口**：明确的输入输出接口定义
5. **更好的可维护性**：模块化设计便于维护和调试

## 迁移指南

从原来的include方式迁移到模块方式：

### 原来的方式：
```systemverilog
module npu_tb;
    `include "itrace.sv"
    // 其他逻辑...
endmodule
```

### 新的方式：
```systemverilog
module npu_tb;
    logic clk, pe_resetn;
    
    itrace_monitor u_itrace_monitor (
        .clk(clk),
        .pe_resetn(pe_resetn)
    );
    
    // 其他逻辑...
endmodule
```
