# ISQ Monitor Include版本使用指南

## 概述

ISQ Monitor Include版本是一个可以直接被include到checker或其他模块中的SystemVerilog代码块。它使用initial块实现，通过顶层绝对路径直接访问信号，无需端口连接。

## 文件说明

### 1. 基础版本
- **`isq_monitor_include.sv`** - 基础include版本，固定配置
- **`checker_with_isq_monitor.sv`** - 使用示例

### 2. 可配置版本（推荐）
- **`isq_monitor_configurable.sv`** - 可配置include版本，支持自定义参数
- **`checker_configurable_example.sv`** - 配置使用示例

## 使用方法

### 🚀 最简单的使用方式

在您的checker模块中添加一行：

```systemverilog
module your_checker;
    // 您的代码...
    
    // 添加ISQ监控 - 就这一行！
    `include "isq_monitor_include.sv"
    
    // 您的其他代码...
endmodule
```

### ⚙️ 可配置使用方式

```systemverilog
module your_checker;
    // 您的代码...
    
    // 可选：自定义配置（在include之前）
    `define ISQ_DUMP_INTERVAL 500           // 每500拍dump一次
    `define ISQ_MAX_COUNT 64                // 队列大小64
    `define ISQ_OUTPUT_FILE "my_isq.log"    // 自定义文件名
    
    // Include ISQ监控
    `include "isq_monitor_configurable.sv"
    
    // 您的其他代码...
endmodule
```

## 配置选项

### 基本配置

| 宏定义 | 默认值 | 说明 |
|--------|--------|------|
| `ISQ_DUMP_INTERVAL` | 1000 | dump间隔（时钟周期） |
| `ISQ_MAX_COUNT` | 32 | 队列最大计数值 |
| `ISQ_OUTPUT_FILE` | "isq_monitor.log" | 输出文件名 |
| `ISQ_DEBUG_OFF` | 未定义 | 定义此宏可关闭调试输出 |

### 路径配置

| 宏定义 | 默认值 | 说明 |
|--------|--------|------|
| `ISQ_TOP_PATH` | `PE_TOP | 顶层模块路径 |
| `ISQ_CLK_PATH` | `PE_TOP.clk | 时钟信号路径 |
| `ISQ_RESET_PATH` | `PE_TOP.pe_resetn | 复位信号路径 |
| `ISQ_MODULE_PATH` | `PE_TOP.u_tcore.tc_tfe.isq | ISQ模块路径 |

## 使用示例

### 示例1：默认配置

```systemverilog
module my_checker;
    // 使用所有默认配置
    `include "isq_monitor_configurable.sv"
    
    // 您的checker代码...
endmodule
```

### 示例2：自定义配置

```systemverilog
module my_checker;
    // 自定义配置
    `define ISQ_DUMP_INTERVAL 200          // 每200拍dump一次
    `define ISQ_MAX_COUNT 128              // 队列大小128
    `define ISQ_OUTPUT_FILE "pe_isq.log"   // 自定义文件名
    `define ISQ_DEBUG_OFF                  // 关闭调试输出
    
    `include "isq_monitor_configurable.sv"
    
    // 您的checker代码...
endmodule
```

### 示例3：不同的设计层次

```systemverilog
module my_checker;
    // 适配不同的设计层次
    `define ISQ_TOP_PATH `MY_SOC_TOP
    `define ISQ_MODULE_PATH `MY_SOC_TOP.cpu.core.isq
    
    `include "isq_monitor_configurable.sv"
    
    // 您的checker代码...
endmodule
```

## 优势

### ✅ 极简集成
- 只需一行`include`语句
- 无需端口连接
- 无需信号声明

### ✅ 直接访问
- 使用绝对路径直接访问顶层信号
- 不依赖模块接口
- 灵活访问任何层次的信号

### ✅ 高度可配置
- 支持自定义所有参数
- 支持不同的设计层次
- 支持多种输出选项

### ✅ 零侵入性
- 不影响现有代码结构
- 可以随时添加或移除
- 不改变模块接口

## 输出文件

生成的CSV文件包含以下字段：
- **Time**: 仿真时间
- **CycleCount**: 周期计数
- **CISQ_VLD_CNT**: CISQ有效计数
- **MISQ_VLD_CNT**: MISQ有效计数
- **CISQ_Utilization**: CISQ利用率(%)
- **MISQ_Utilization**: MISQ利用率(%)
- **Dispatch_Port0**: 端口0状态
- **Dispatch_Port1**: 端口1状态

## 注意事项

### ⚠️ 变量名冲突
- include版本使用全局变量，注意避免命名冲突
- 变量名都以`isq_`前缀开头

### ⚠️ 多实例限制
- 当前版本不支持在同一模块中多次include
- 如需监控多个ISQ，需要修改变量名

### ⚠️ 路径依赖
- 依赖特定的信号路径
- 如果设计层次改变，需要更新路径配置

## 故障排除

### Q: 编译错误 "信号未找到"
**A**: 检查并更新信号路径配置：
```systemverilog
`define ISQ_MODULE_PATH `YOUR_ACTUAL_PATH.to.isq
```

### Q: 文件无法生成
**A**: 检查文件权限和路径，确保仿真器有写入权限

### Q: 数据异常
**A**: 确认`ISQ_MAX_COUNT`与实际队列大小匹配

### Q: 想要多个监控实例
**A**: 复制文件并修改变量名前缀，或使用模块化版本

## 与其他版本对比

| 特性 | Include版本 | 模块版本 |
|------|-------------|----------|
| 集成难度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 灵活性 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| 可重用性 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 多实例支持 | ⭐⭐ | ⭐⭐⭐⭐⭐ |
| 推荐场景 | 快速验证、单一监控 | 正式项目、多实例 |

## 最佳实践

1. **快速验证**: 使用基础版本快速添加监控
2. **正式项目**: 使用可配置版本并设置合适参数
3. **多PE监控**: 考虑使用模块化版本
4. **路径管理**: 将路径配置集中管理
5. **文件命名**: 使用有意义的输出文件名
