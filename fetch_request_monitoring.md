# 取指请求时间统计功能说明 - 32bit粒度管理版本

## 功能概述

在原有的指令性能监控基础上，新增了基于32bit粒度的取指请求时间统计功能。该功能采用简洁高效的设计：
- **取指请求时**：64bit取指请求直接拆分成两个32bit请求，重复地址自动覆盖
- **取指完成时**：根据返回的PC精确匹配32bit指令，获取取指时间并删除对应请求

## 核心特性

### 1. 32bit粒度管理
- **统一粒度**: 所有取指请求都以32bit为基本单位进行管理
- **简化设计**: 无需处理复杂的地址空间重叠问题
- **直接映射**: 每个32bit指令地址对应一个取指请求条目
- **自动覆盖**: 相同地址的新请求自动覆盖旧请求

### 2. 取指请求处理流程
- **64bit拆分**: `fetch_issue` 信号拉高时，将64bit请求拆分成两个32bit请求
- **地址检查**: 检查两个32bit地址是否已存在于队列中
- **覆盖更新**: 如果地址已存在，用新的取指时间覆盖旧的时间
- **新增条目**: 如果地址不存在，添加新的32bit取指请求

### 3. 取指完成处理流程

#### Tile指令处理：
- **指令创建**: 在tile指令收集完成时
- **精确匹配**: 根据指令PC地址在队列中查找完全匹配的32bit请求
- **时间获取**: 获取对应的取指开始时间
- **条目删除**: 从队列中删除已完成的32bit取指请求
- **时间设置**: 将获取的取指时间设置为指令的`fetch_start`时间

#### 非Tile指令处理：
- **指令返回**: `ifu_i0_valid & ifu_i0_ready` 或 `ifu_i1_valid & ifu_i1_ready`
- **精确匹配**: 根据返回的PC地址在队列中查找完全匹配的32bit请求
- **条目删除**: 如果找到匹配的取指请求，从队列中删除该条目
- **指令丢弃**: 非tile指令不进入性能统计，但会清理对应的取指请求

## 数据结构

### 32bit取指请求结构
```systemverilog
typedef struct {
    logic [31:0] req_addr;      // 32bit取指地址
    realtime fetch_time;        // 取指开始时间
} fetch_req_32bit_t;
```

### 队列管理
- `fetch_req_queue[$]`: 32bit取指请求队列（统一管理所有32bit取指请求）

## 输出文件

### itrace.log (更新)
新增了decode阶段和取指开始时间，形成完整的指令流水线：

完整字段列表：
- `InstrID`: 指令ID
- `PC`: 指令地址
- `Instruction`: 完整指令数据
- `TileInstr`: Tile指令数据
- `Disassembly`: 反编译字符串
- `ITag`: 指令标签
- `FetchStartTime`: 取指开始的绝对时间 (新增)
- `FetchTime`: 取指阶段耗时 (从取指开始到取指完成)
- `DecodeTime`: 译码阶段耗时 (从取指完成到dispatch开始) (新增)
- `DispatchTime`: 分发阶段耗时
- `ExecuteTime`: 执行阶段耗时
- `TotalTime`: 总耗时 (从取指开始到执行完成)

### 调试信息
在 `itrace_debug.log` 中新增：
- 取指请求发出的记录
- 取指请求与指令关联的信息

## 关键逻辑

### 1. 32bit取指请求添加函数
```systemverilog
// 添加32bit取指请求，如果地址重复则覆盖
function automatic void add_32bit_fetch_request(logic [31:0] req_addr, realtime fetch_time);
    // 检查是否已存在相同地址的请求
    for (int i = 0; i < fetch_req_queue.size(); i++) begin
        if (fetch_req_queue[i].req_addr == req_addr) begin
            // 覆盖现有请求
            fetch_req_queue[i].fetch_time = fetch_time;
            return;
        end
    end

    // 如果不存在，添加新请求
    fetch_req_32bit_t new_req;
    new_req.req_addr = req_addr;
    new_req.fetch_time = fetch_time;
    fetch_req_queue.push_back(new_req);
endfunction
```

### 2. 64bit取指请求处理函数
```systemverilog
// 处理64bit取指请求，拆分成两个32bit请求
function automatic void process_64bit_fetch_request(logic [31:0] fetch_addr, realtime fetch_time);
    logic [31:0] addr1 = fetch_addr;
    logic [31:0] addr2 = fetch_addr + 4;

    // 添加两个32bit取指请求
    add_32bit_fetch_request(addr1, fetch_time);
    add_32bit_fetch_request(addr2, fetch_time);
endfunction
```

### 3. 指令完成处理函数
```systemverilog
// 根据PC地址查找并删除32bit取指请求，返回取指时间
function automatic realtime find_and_remove_fetch_time(logic [31:0] pc_addr);
    for (int i = 0; i < fetch_req_queue.size(); i++) begin
        if (fetch_req_queue[i].req_addr == pc_addr) begin
            realtime fetch_time = fetch_req_queue[i].fetch_time;

            // 删除该条目
            fetch_req_queue.delete(i);
            return fetch_time;
        end
    end
    return 0; // 未找到对应的取指请求
endfunction
```

### 4. 指令时间设置逻辑
```systemverilog
// Tile指令：在指令创建时设置fetch和decode时间
realtime fetch_req_time = find_and_remove_fetch_time(collector_start_pc);
new_instr.fetch_start = (fetch_req_time > 0) ? fetch_req_time : collector_start_time;
new_instr.fetch_end = $realtime;  // 或 collector_end_time
new_instr.decode_start = $realtime;  // decode开始时间 = 取指完成时间
new_instr.decode_end = 0;  // decode结束时间将在dispatch时设置
```

### 5. Dispatch阶段时间设置
```systemverilog
// 在dispatch时设置decode结束时间和dispatch开始时间
instr_queue[i].decode_end = $realtime;  // decode结束时间 = dispatch开始时间
instr_queue[i].dispatch_start = $realtime;
```

### 6. 非Tile指令处理逻辑
```systemverilog
// 非Tile指令：查询并删除对应的取指请求（如果存在）
if (ch0_active) begin
    realtime fetch_req_time_ch0 = find_and_remove_fetch_time(ifu_i0_pc);
    // 指令被丢弃，但取指请求被清理
end

if (ch1_active) begin
    realtime fetch_req_time_ch1 = find_and_remove_fetch_time(ifu_i1_pc);
    // 指令被丢弃，但取指请求被清理
end
```

### 7. 完整的流水线阶段
现在指令处理包含完整的连续流水线阶段：

1. **Fetch阶段**: 从取指请求开始到取指完成
2. **Decode阶段**: 从取指完成到dispatch开始
3. **Dispatch阶段**: 从dispatch开始到dispatch完成
4. **Execute阶段**: 从execute开始到execute完成

所有阶段时间连续，无间隙。

### 取指请求数据结构
```systemverilog
typedef struct {
    logic [31:0] req_addr;      // 取指请求地址(按32bit指令对齐)
    realtime req_start_time;    // 请求开始时间
    realtime req_end_time;      // 请求结束时间(当有指令从该请求返回时更新)
    bit [1:0] returned_count;   // 已返回的32bit指令数量(最多2个)
    bit completed;              // 是否已完成
} fetch_req_t;
```

### 指令性能数据结构更新
```systemverilog
typedef struct {
    logic [31:0] pc;
    logic [127:0] instr;
    logic [50:0] tinstr;
    string disasm_str;
    logic [31:0] itag;
    realtime fetch_req_start, fetch_req_end;  // 新增：取指请求时间
    realtime fetch_start, fetch_end;
    realtime dispatch_start, dispatch_end;
    realtime execute_start, execute_end;
    bit valid;
} instr_perf_t;
```

### 重叠处理策略
1. **多个匹配时选择最新**：当一个PC地址匹配多个取指请求时，选择最新的请求
2. **返回计数管理**：每个取指请求最多返回2个32bit指令
3. **完成条件**：当返回计数达到2时，标记请求完成

### 32bit粒度处理示例
```
初始状态：
fetch_req_queue = []

时间T1: fetch_issue=1, req_addr=0x1000
- 拆分64bit请求：0x1000 -> [0x1000, 0x1004]
- 添加32bit请求：0x1000(T1), 0x1004(T1)
- fetch_req_queue = [0x1000(T1), 0x1004(T1)]

时间T2: fetch_issue=1, req_addr=0x1004
- 拆分64bit请求：0x1004 -> [0x1004, 0x1008]
- 检查0x1004：已存在，覆盖为0x1004(T2)
- 添加0x1008：新地址，添加0x1008(T2)
- fetch_req_queue = [0x1000(T1), 0x1004(T2), 0x1008(T2)]

指令返回处理：
- PC=0x1000 Tile指令创建：找到0x1000(T1)，设置fetch_start=T1，decode_start=当前时间，删除该条目
- PC=0x1004 非Tile指令返回：找到0x1004(T2)，删除该条目（指令被丢弃）
- PC=0x1008 Tile指令创建：找到0x1008(T2)，设置fetch_start=T2，decode_start=当前时间，删除该条目

Dispatch处理：
- 指令dispatch时：设置decode_end=当前时间，dispatch_start=当前时间

最终状态：
fetch_req_queue = []
```

## 信号连接

新增的信号连接：
```systemverilog
// 取指请求信号
assign fetch_issue = `PE_TOP.u_ax45mpv_cluster_wrapper.u_complex0_wrapper.u_complex.u_core0.ax45mpv_core.kv_core.kv_ifu.fetch_issue;
assign req_addr = `PE_TOP.u_ax45mpv_cluster_wrapper.u_complex0_wrapper.u_complex.u_core0.ax45mpv_core.kv_core.kv_ifu.req_addr;

// 时钟和复位信号
assign clk = `PE_TOP.clk;
assign pe_resetn = `PE_TOP.pe_resetn;
```

## 使用方法

1. 确保在testbench中包含 `itrace.sv` 文件
2. 运行仿真后，检查生成的 `itrace.log` 文件中的完整流水线时间统计
3. 如需调试，可查看 `itrace_debug.log` 中的详细信息

## 注意事项

1. **完整流水线**: 新增了decode阶段，形成完整的连续流水线：Fetch → Decode → Dispatch → Execute
2. **绝对时间**: 新增了`FetchStartTime`字段，提供指令开始的绝对时间戳
3. **连续阶段**: 所有阶段时间连续，decode开始时间 = fetch结束时间，dispatch开始时间 = decode结束时间
4. `fetch_start`时间现在使用取指请求时间，提供更准确的取指开始时间
5. 如果指令无法匹配到取指请求，将使用原有的`collector_start_time`作为备选
6. **非Tile指令处理**：非tile指令虽然不进入性能统计，但会查询并清理对应的取指请求
7. **队列清理完整性**：确保所有返回的指令（无论是否为tile指令）都会清理对应的取指请求
8. **简化逻辑**：移除了第一条指令丢弃逻辑和复杂的通道时间跟踪
9. **统一处理**：所有tile指令都会被添加到性能统计队列中
10. 支持复位期间的状态清理
11. 取指请求时间从`fetch_issue`信号拉高开始计算
