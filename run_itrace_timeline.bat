@echo off
echo ============================================================
echo ITrace Timeline Visualizer
echo ============================================================
echo.

REM 检查Python是否可用
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: Python未安装或不在PATH中
    echo 请安装Python 3.6或更高版本
    pause
    exit /b 1
)

REM 检查pandas是否安装
python -c "import pandas" >nul 2>&1
if errorlevel 1 (
    echo 警告: pandas未安装，正在尝试安装...
    pip install pandas
    if errorlevel 1 (
        echo 错误: 无法安装pandas
        echo 请手动运行: pip install pandas
        pause
        exit /b 1
    )
)

REM 检查是否存在itrace.log文件
if exist "itrace.log" (
    echo 找到itrace.log文件，正在生成可视化...
    python itrace_timeline_visualizer.py --open
) else (
    echo 未找到itrace.log文件
    echo.
    echo 选择操作:
    echo 1. 生成测试数据并可视化
    echo 2. 指定itrace.log文件路径
    echo 3. 退出
    echo.
    set /p choice="请输入选择 (1-3): "
    
    if "%choice%"=="1" (
        echo 正在生成测试数据...
        python test_itrace_visualizer.py
        if exist "itrace_timeline.html" (
            echo 正在打开HTML文件...
            start itrace_timeline.html
        )
    ) else (
        if "%choice%"=="2" (
            set /p filepath="请输入itrace.log文件的完整路径: "
            if exist "%filepath%" (
                python itrace_timeline_visualizer.py "%filepath%" --open
            ) else (
                echo 错误: 指定的文件不存在
            )
        ) else (
            echo 退出程序
            exit /b 0
        )
    )
)

echo.
echo 完成！
pause
