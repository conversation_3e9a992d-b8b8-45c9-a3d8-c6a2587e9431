#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ISQ Monitor Visualization Tool
Read ISQ monitor output file and generate real-time utilization line charts

Usage:
python isq_visualizer.py [filename]

If no filename is specified, defaults to isq_monitor.log
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import numpy as np
import argparse
import os
import sys
from datetime import datetime

class ISQVisualizer:
    def __init__(self, filename='isq_monitor.log', update_interval=1000):
        """
        初始化ISQ可视化器
        
        Args:
            filename: ISQ监控输出文件名
            update_interval: 图表更新间隔(毫秒)
        """
        self.filename = filename
        self.update_interval = update_interval
        self.fig, self.ax = plt.subplots(figsize=(12, 8))
        self.data = None
        self.last_modified = 0
        
        # 设置图表样式
        self.setup_plot()
        
    def setup_plot(self):
        """设置图表样式和布局"""
        self.ax.set_xlabel('Simulation Time', fontsize=12)
        self.ax.set_ylabel('Utilization (%)', fontsize=12)
        self.ax.set_title('ISQ Utilization Over Time', fontsize=14, fontweight='bold')
        self.ax.grid(True, alpha=0.3)
        self.ax.set_ylim(0, 100)
        
        # 设置图例
        self.cisq_line, = self.ax.plot([], [], 'b-', linewidth=2, label='CISQ Utilization', marker='o', markersize=3)
        self.misq_line, = self.ax.plot([], [], 'r-', linewidth=2, label='MISQ Utilization', marker='s', markersize=3)
        
        self.ax.legend(loc='upper left', fontsize=10)
        
        # 添加统计信息文本框
        self.stats_text = self.ax.text(0.02, 0.98, '', transform=self.ax.transAxes, 
                                      verticalalignment='top', fontsize=9,
                                      bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
    def read_data(self):
        """读取ISQ监控数据文件"""
        try:
            if not os.path.exists(self.filename):
                print(f"警告: 文件 {self.filename} 不存在")
                return None
                
            # 检查文件是否被修改
            current_modified = os.path.getmtime(self.filename)
            if current_modified <= self.last_modified:
                return self.data  # 文件未更新，返回缓存数据
                
            self.last_modified = current_modified
            
            # 读取CSV文件
            df = pd.read_csv(self.filename)
            
            # 验证必要的列是否存在
            required_columns = ['Time', 'CycleCount', 'CISQ_VLD_CNT', 'MISQ_VLD_CNT', 
                              'CISQ_Utilization', 'MISQ_Utilization']
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"错误: 缺少必要的列: {missing_columns}")
                return None
                
            self.data = df
            return df
            
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return None
    
    def update_plot(self, frame):
        """更新图表数据"""
        df = self.read_data()
        
        if df is None or df.empty:
            return self.cisq_line, self.misq_line, self.stats_text
        
        # 更新数据
        times = df['Time'].values
        cisq_util = df['CISQ_Utilization'].values
        misq_util = df['MISQ_Utilization'].values
        
        # 更新折线图
        self.cisq_line.set_data(times, cisq_util)
        self.misq_line.set_data(times, misq_util)
        
        # 自动调整X轴范围
        if len(times) > 0:
            self.ax.set_xlim(times[0], times[-1] * 1.05)
        
        # 更新统计信息
        if len(df) > 0:
            stats_text = self.generate_stats_text(df)
            self.stats_text.set_text(stats_text)
        
        return self.cisq_line, self.misq_line, self.stats_text
    
    def generate_stats_text(self, df):
        """生成统计信息文本"""
        cisq_avg = df['CISQ_Utilization'].mean()
        misq_avg = df['MISQ_Utilization'].mean()
        cisq_max = df['CISQ_Utilization'].max()
        misq_max = df['MISQ_Utilization'].max()
        cisq_current = df['CISQ_Utilization'].iloc[-1]
        misq_current = df['MISQ_Utilization'].iloc[-1]
        total_cycles = df['CycleCount'].iloc[-1] if len(df) > 0 else 0
        
        stats = f"""实时统计信息:
总周期数: {total_cycles:,}
数据点数: {len(df):,}

当前利用率:
CISQ: {cisq_current:.1f}%
MISQ: {misq_current:.1f}%

平均利用率:
CISQ: {cisq_avg:.1f}%
MISQ: {misq_avg:.1f}%

最大利用率:
CISQ: {cisq_max:.1f}%
MISQ: {misq_max:.1f}%"""
        
        return stats
    
    def start_animation(self):
        """启动动画显示"""
        print(f"开始监控文件: {self.filename}")
        print(f"更新间隔: {self.update_interval}ms")
        print("按 Ctrl+C 退出")
        
        # 创建动画
        ani = animation.FuncAnimation(
            self.fig, self.update_plot, interval=self.update_interval,
            blit=False, cache_frame_data=False
        )
        
        # 显示图表
        plt.tight_layout()
        plt.show()
        
        return ani

def create_static_plot(filename):
    """创建静态图表（一次性读取所有数据）"""
    try:
        df = pd.read_csv(filename)
        
        plt.figure(figsize=(12, 8))
        
        # 绘制折线图
        plt.plot(df['Time'], df['CISQ_Utilization'], 'b-', linewidth=2, 
                label='CISQ Utilization', marker='o', markersize=3)
        plt.plot(df['Time'], df['MISQ_Utilization'], 'r-', linewidth=2, 
                label='MISQ Utilization', marker='s', markersize=3)
        
        # 设置图表
        plt.xlabel('Simulation Time', fontsize=12)
        plt.ylabel('Utilization (%)', fontsize=12)
        plt.title('ISQ Utilization Over Time', fontsize=14, fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.ylim(0, 100)
        plt.legend(fontsize=10)
        
        # 添加统计信息
        cisq_avg = df['CISQ_Utilization'].mean()
        misq_avg = df['MISQ_Utilization'].mean()
        cisq_max = df['CISQ_Utilization'].max()
        misq_max = df['MISQ_Utilization'].max()
        
        stats_text = f"""统计信息:
总数据点: {len(df):,}
CISQ平均: {cisq_avg:.1f}%
MISQ平均: {misq_avg:.1f}%
CISQ最大: {cisq_max:.1f}%
MISQ最大: {misq_max:.1f}%"""
        
        plt.text(0.02, 0.98, stats_text, transform=plt.gca().transAxes,
                verticalalignment='top', fontsize=9,
                bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.8))
        
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"创建静态图表时出错: {e}")

def main():
    parser = argparse.ArgumentParser(description='ISQ Monitor Visualization Tool')
    parser.add_argument('filename', nargs='?', default='isq_monitor.log',
                       help='ISQ监控输出文件名 (默认: isq_monitor.log)')
    parser.add_argument('--static', action='store_true',
                       help='创建静态图表而不是实时更新')
    parser.add_argument('--interval', type=int, default=1000,
                       help='实时更新间隔(毫秒) (默认: 1000)')
    
    args = parser.parse_args()
    
    # 检查文件是否存在
    if not os.path.exists(args.filename):
        print(f"错误: 文件 {args.filename} 不存在")
        print("请确保ISQ监控器已经运行并生成了输出文件")
        sys.exit(1)
    
    print(f"ISQ Monitor Visualization Tool")
    print(f"文件: {args.filename}")
    print(f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 50)
    
    if args.static:
        print("创建静态图表...")
        create_static_plot(args.filename)
    else:
        print("启动实时监控...")
        visualizer = ISQVisualizer(args.filename, args.interval)
        try:
            ani = visualizer.start_animation()
        except KeyboardInterrupt:
            print("\n监控已停止")

if __name__ == "__main__":
    main()
