// 测试sync/wait指令识别功能的简单测试用例

module test_sync_wait_instructions;
    import tile_decoder_pkg::*;
    
    // 测试用例
    initial begin
        logic [31:0] test_instructions[8];
        logic expected_results[8];
        string instr_names[8];
        
        // 定义测试指令
        test_instructions[0] = 32'hfb0012ab; // twait (tuop=101, ctrluop=001, waitop=111, bit26=0)
        expected_results[0] = 1'b1;
        instr_names[0] = "twait";
        
        test_instructions[1] = 32'hfb4012ab; // twait.mem (tuop=101, ctrluop=001, waitop=111, bit26=1)
        expected_results[1] = 1'b1;
        instr_names[1] = "twait.mem";
        
        test_instructions[2] = 32'hfb801000; // ace_bsync (tuop=111, ace_misc_en=1, miscop=000)
        expected_results[2] = 1'b1;
        instr_names[2] = "ace_bsync";
        
        test_instructions[3] = 32'hfb802000; // ace_nbsync (tuop=111, ace_misc_en=1, miscop=010)
        expected_results[3] = 1'b1;
        instr_names[3] = "ace_nbsync";
        
        test_instructions[4] = 32'hfb001000; // 普通tile指令 (tuop=000)
        expected_results[4] = 1'b0;
        instr_names[4] = "normal_tile";
        
        test_instructions[5] = 32'hfb002000; // 普通tile指令 (tuop=001)
        expected_results[5] = 1'b0;
        instr_names[5] = "tmma";
        
        test_instructions[6] = 32'h12345678; // 非tile指令
        expected_results[6] = 1'b0;
        instr_names[6] = "non_tile";
        
        test_instructions[7] = 32'hfb803000; // 其他ACE指令 (tuop=111, ace_misc_en=1, miscop=011)
        expected_results[7] = 1'b0;
        instr_names[7] = "other_ace";
        
        $display("=== 测试sync/wait指令识别功能 ===");
        
        // 执行测试
        for (int i = 0; i < 8; i++) begin
            logic result = is_sync_or_wait_instruction(test_instructions[i]);
            string disasm = disassemble_instruction({96'h0, test_instructions[i]}, INSTR_32BIT);
            
            $display("测试 %0d: 指令=0x%08h, 期望=%b, 实际=%b, 反编译=\"%s\", %s", 
                    i, test_instructions[i], expected_results[i], result, disasm,
                    (result == expected_results[i]) ? "PASS" : "FAIL");
                    
            if (result != expected_results[i]) begin
                $display("ERROR: 测试失败！");
                $finish;
            end
        end
        
        $display("=== 所有测试通过！ ===");
        $finish;
    end
    
endmodule
