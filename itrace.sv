
// NPU Performance Monitor Module
// Import tile decoder package for instruction identification
import tile_decoder_pkg::*;

module itrace_monitor (
    input logic clk,
    input logic pe_resetn
);

// Instruction tracking structure
typedef struct {
    logic [47:0] pc;      // 扩展到48位PC
    logic [127:0] instr;  // 128位宽，存储完整的tile指令
    logic [50:0] tinstr;  // 51位宽，存储dispatch阶段的tile指令
    string disasm_str;    // 反编译指令字符串
    logic [31:0] itag;  // 添加instruction tag
    realtime fetch_start, fetch_end;  // fetch_start现在使用取指请求时间
    realtime decode_start, decode_end;  // decode阶段：从取指完成到dispatch开始
    realtime dispatch_start, dispatch_end;
    realtime execute_start, execute_end;
    bit valid;
} instr_perf_t;

integer perf_file;
integer instr_id = 0;
instr_perf_t instr_queue[$];

// Debug开关和相关变量
bit ITRACE_DEBUG_ENABLE;  // 通过$test$plusargs动态获取debug开关
integer debug_file;

// 取指请求跟踪结构（支持48bit PC比较）
typedef struct {
    logic [47:0] req_addr_48bit; // 48bit完整取指地址（用于取指请求时的比较）
    logic [31:0] req_addr_32bit; // 32bit取指地址（用于其他时候的比较）
    realtime fetch_time;         // 取指开始时间
} fetch_req_t;

fetch_req_t fetch_req_queue[$];

// 函数：添加取指请求，如果地址重复则覆盖（取指请求时使用48bit比较）
function automatic void add_fetch_request(logic [47:0] req_addr_48bit, realtime fetch_time);
    fetch_req_t new_req;
    logic [31:0] req_addr_32bit = req_addr_48bit[31:0];

    // 检查是否已存在相同48bit地址的请求（取指请求时使用48bit比较）
    for (int i = 0; i < fetch_req_queue.size(); i++) begin
        if (fetch_req_queue[i].req_addr_48bit == req_addr_48bit) begin
            // 覆盖现有请求
            fetch_req_queue[i].fetch_time = fetch_time;

            if (ITRACE_DEBUG_ENABLE) begin
                $fwrite(debug_file, "Time: %0t - Overwrite existing fetch request: Addr=0x%012h, NewTime=%.2f\n",
                       $realtime, req_addr_48bit, fetch_time);
            end
            return;
        end
    end

    // 如果不存在，添加新请求
    new_req.req_addr_48bit = req_addr_48bit;
    new_req.req_addr_32bit = req_addr_32bit;
    new_req.fetch_time = fetch_time;
    fetch_req_queue.push_back(new_req);

    if (ITRACE_DEBUG_ENABLE) begin
        $fwrite(debug_file, "Time: %0t - Added new fetch request: Addr=0x%012h, Time=%.2f\n",
               $realtime, req_addr_48bit, fetch_time);
    end
endfunction

// 函数：处理64bit取指请求，拆分成两个取指请求
function automatic void process_64bit_fetch_request(logic [47:0] fetch_addr_48bit, realtime fetch_time);
    logic [47:0] addr1_48bit = fetch_addr_48bit;
    logic [47:0] addr2_48bit = fetch_addr_48bit + 4;

    // 添加两个取指请求
    add_fetch_request(addr1_48bit, fetch_time);
    add_fetch_request(addr2_48bit, fetch_time);

    if (ITRACE_DEBUG_ENABLE) begin
        $fwrite(debug_file, "Time: %0t - Processed 64bit fetch: 0x%012h -> [0x%012h, 0x%012h], Queue_Size=%0d\n",
               $realtime, fetch_addr_48bit, addr1_48bit, addr2_48bit, fetch_req_queue.size());
    end
endfunction

// 函数：根据PC地址查找并删除取指请求，返回取指时间（其他时候只比较低32bit）
function automatic realtime find_and_remove_fetch_time(logic [47:0] pc_addr_48bit);
    logic [31:0] pc_addr_32bit = pc_addr_48bit[31:0];

    for (int i = 0; i < fetch_req_queue.size(); i++) begin
        // 其他时候只比较低32bit
        if (fetch_req_queue[i].req_addr_32bit == pc_addr_32bit) begin
            realtime fetch_time = fetch_req_queue[i].fetch_time;

            if (ITRACE_DEBUG_ENABLE) begin
                $fwrite(debug_file, "Time: %0t - Found and removed fetch request: PC=0x%012h(32bit=0x%08h), FetchTime=%.2f, Queue_Size=%0d\n",
                       $realtime, pc_addr_48bit, pc_addr_32bit, fetch_time, fetch_req_queue.size() - 1);
            end

            // 删除该条目
            fetch_req_queue.delete(i);
            return fetch_time;
        end
    end

    if (ITRACE_DEBUG_ENABLE) begin
        $fwrite(debug_file, "Time: %0t - No fetch request found for PC=0x%012h(32bit=0x%08h), Queue_Size=%0d\n",
               $realtime, pc_addr_48bit, pc_addr_32bit, fetch_req_queue.size());
    end

    return 0; // 未找到对应的取指请求
endfunction

// 函数：根据32bit PC查找并返回48bit PC和取指时间（用于获取取指请求时的完整PC）
function automatic void find_and_remove_fetch_info(logic [31:0] pc_addr_32bit, output logic [47:0] pc_addr_48bit, output realtime fetch_time);
    pc_addr_48bit = 48'h0;
    fetch_time = 0;

    for (int i = 0; i < fetch_req_queue.size(); i++) begin
        // 比较低32bit
        if (fetch_req_queue[i].req_addr_32bit == pc_addr_32bit) begin
            pc_addr_48bit = fetch_req_queue[i].req_addr_48bit;  // 返回取指请求时的48bit PC
            fetch_time = fetch_req_queue[i].fetch_time;

            if (ITRACE_DEBUG_ENABLE) begin
                $fwrite(debug_file, "Time: %0t - Found and removed fetch request: 32bit_PC=0x%08h -> 48bit_PC=0x%012h, FetchTime=%.2f, Queue_Size=%0d\n",
                       $realtime, pc_addr_32bit, pc_addr_48bit, fetch_time, fetch_req_queue.size() - 1);
            end

            // 删除该条目
            fetch_req_queue.delete(i);
            return;
        end
    end

    if (ITRACE_DEBUG_ENABLE) begin
        $fwrite(debug_file, "Time: %0t - No fetch request found for 32bit_PC=0x%08h, Queue_Size=%0d\n",
               $realtime, pc_addr_32bit, fetch_req_queue.size());
    end
endfunction



    // 取指请求相关信号
    logic fetch_issue;
    logic [47:0] req_addr;  // 扩展到48位

    // 信号连接 - 避免在forever块中重复赋值
    logic ifu_i0_valid, ifu_i0_ready, ifu_i1_valid, ifu_i1_ready;
    logic [47:0] ifu_i0_pc, ifu_i1_pc;  // 扩展到48位
    logic [31:0] ifu_i0_instr, ifu_i1_instr;
    logic idu_isq_uop0_valid, idu_isq_uop1_valid;
    logic [50:0] idu_isq_uop0_opcode, idu_isq_uop1_opcode;
    logic [31:0] ctl_isq_uop0_itag, ctl_isq_uop1_itag;
    // 通道活动状态信号
    logic ch0_active, ch1_active;
    // Tile指令检测信号
    logic ch0_is_tile, ch1_is_tile;
    logic both_channels_active;

    // 指令断流统计相关信号和变量
    logic instruction_stall;  // 指令断流信号：两个通道的valid都为0
    logic thread_done;  // 线程完成信号
    realtime stall_monitor_start_time;  // 断流监控开始时间（0x9d02指令取指时间）
    realtime stall_monitor_end_time;  // 断流监控结束时间（thread_done时间）
    realtime total_monitor_time;  // 总监控时间
    realtime total_stall_time;  // 总断流时间
    realtime stall_start_time;  // 当前断流开始时间
    bit stall_monitor_active;  // 断流监控期间标志
    bit stall_in_progress;  // 当前是否在断流中
    bit monitor_started;  // 是否已经开始监控
    integer total_tile_count;  // 总tile指令数量

    // 功能单元利用率统计相关信号和变量
    logic tmac_executing;  // TMAC执行信号
    logic tsfu_executing;  // TSFU执行信号
    logic talu_executing;  // TALU执行信号
    logic tld_executing;   // TLD执行信号
    logic tst_executing;   // TST执行信号

    // 利用率统计计数器
    longint tmac_active_cycles;  // TMAC活跃周期数
    longint tsfu_active_cycles;  // TSFU活跃周期数
    longint talu_active_cycles;  // TALU活跃周期数
    longint tld_active_cycles;   // TLD活跃周期数
    longint tst_active_cycles;   // TST活跃周期数
    longint total_monitor_cycles; // 总监控周期数

    // 0x9d02指令检测相关
    parameter logic [15:0] START_OPCODE = 16'h9d02;  // c.jalr s10指令的opcode

    // 取指请求信号连接 - 通过backdoor方式直接连接到顶层信号
    assign fetch_issue = `CORE0.ax45mpv_core.kv_core.kv_ifu.fetch_issue;
    // 如果硬件信号是32位，则零扩展到48位；如果是48位，直接连接
    assign req_addr = {`CORE0.ax45mpv_core.kv_core.kv_ifu.req_addr};

    assign ifu_i0_valid = `CORE0.ax45mpv_core.kv_core.kv_ifu.ifu_i0_valid;
    assign ifu_i0_ready = `CORE0.ax45mpv_core.kv_core.kv_ifu.ifu_i0_ready;
    // 如果硬件信号是32位，则零扩展到48位；如果是48位，直接连接
    assign ifu_i0_pc = {16'h0, `CORE0.ax45mpv_core.kv_core.kv_ifu.ifu_i0_pc};
    assign ifu_i0_instr = `CORE0.ax45mpv_core.kv_core.kv_ifu.ifu_i0_instr;

    assign ifu_i1_valid = `CORE0.ax45mpv_core.kv_core.kv_ifu.ifu_i1_valid;
    assign ifu_i1_ready = `CORE0.ax45mpv_core.kv_core.kv_ifu.ifu_i1_ready;
    // 如果硬件信号是32位，则零扩展到48位；如果是48位，直接连接
    assign ifu_i1_pc = {16'h0, `CORE0.ax45mpv_core.kv_core.kv_ifu.ifu_i1_pc};
    assign ifu_i1_instr = `CORE0.ax45mpv_core.kv_core.kv_ifu.ifu_i1_instr;

    assign idu_isq_uop0_valid = `TCORE.tc_tfe.isq.idu_isq_uop0_valid;
    assign idu_isq_uop0_opcode = `TCORE.tc_tfe.isq.idu_isq_uop0_opcode;
    assign ctl_isq_uop0_itag = `TCORE.tc_tfe.isq.ctl_isq_uop0_itag;

    assign idu_isq_uop1_valid = `TCORE.tc_tfe.isq.idu_isq_uop1_valid;
    assign idu_isq_uop1_opcode = `TCORE.tc_tfe.isq.idu_isq_uop1_opcode;
    assign ctl_isq_uop1_itag = `TCORE.tc_tfe.isq.ctl_isq_uop1_itag;


    assign ch0_active = ifu_i0_valid & ifu_i0_ready;
    assign ch1_active = ifu_i1_valid & ifu_i1_ready;

    // Tile指令检测assign语句
    assign ch0_is_tile = ch0_active && is_tile_instruction(ifu_i0_instr);
    assign ch1_is_tile = ch1_active && is_tile_instruction(ifu_i1_instr);
    assign both_channels_active = ch0_active && ch1_active;

    // Sync/Wait指令检测assign语句
    logic ch0_is_sync_wait, ch1_is_sync_wait;
    assign ch0_is_sync_wait = ch0_active && is_sync_or_wait_instruction(ifu_i0_instr);
    assign ch1_is_sync_wait = ch1_active && is_sync_or_wait_instruction(ifu_i1_instr);

    // 指令断流检测assign语句
    assign instruction_stall = (!ifu_i0_valid && !ifu_i1_valid) && pe_resetn;

    // 线程完成信号连接
    assign thread_done = `TCORE.tc_tfe.ibuf.tfe_csr.t0_csr_tbl.tcore2bd_thread_done;

    // 功能单元执行信号连接
    assign tmac_executing = `TCORE.tc_tfe.tmac_tfe_executing;
    assign tsfu_executing = `TCORE.tc_tfe.pm_tfe_tsfu_executing;
    assign talu_executing = `TCORE.tc_tfe.pm_tfe_talu_executing;
    assign tld_executing = `TCORE.tc_tfe.pm_tfe_tld_executing;
    assign tst_executing = `TCORE.tc_tfe.pm_tfe_tst_executing;

initial begin

    // 取指请求时间变量声明
    realtime fetch_req_time, fetch_req_time_ch0, fetch_req_time_ch1, fetch_req_time_ch1_only;
    realtime fetch_req_time_multi, fetch_req_time_continue;

    



    // Tile指令收集器 - 用于合并多字指令
    instr_collector_t active_collector;
    bit collector_active = 0;
    realtime collector_start_time = 0;  // 收集器开始时间
    realtime collector_end_time = 0;    // 收集器结束时间
    logic [47:0] collector_start_pc = 0; // 第一个tile字的起始PC（48位）
    realtime collector_fetch_time = 0;  // 收集器对应的取指时间
    
    // 初始化指令断流统计变量
    stall_monitor_start_time = 0;
    stall_monitor_end_time = 0;
    total_monitor_time = 0;
    total_stall_time = 0;
    stall_start_time = 0;
    stall_monitor_active = 0;
    stall_in_progress = 0;
    monitor_started = 0;
    total_tile_count = 0;

    // 初始化功能单元利用率统计变量
    tmac_active_cycles = 0;
    tsfu_active_cycles = 0;
    talu_active_cycles = 0;
    tld_active_cycles = 0;
    tst_active_cycles = 0;
    total_monitor_cycles = 0;

    // 通过$test$plusargs动态获取DEBUG开关值
    ITRACE_DEBUG_ENABLE = $test$plusargs("ITRACE_DEBUG_ENABLE");

    // Open performance log file
    perf_file = $fopen("itrace.log", "w");
    $fwrite(perf_file, "InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime\n");

    // Open debug log file if debug is enabled
    if (ITRACE_DEBUG_ENABLE) begin
        debug_file = $fopen("itrace_debug.log", "w");
        $fwrite(debug_file, "=== ITRACE DEBUG LOG ===\n");
        $fwrite(debug_file, "Time: %0t - Debug logging started\n", $realtime);
        $display("Debug logging enabled - output to itrace_debug.log");
    end
    
    forever begin
        @(posedge clk);

        // Debug信息 - 基本信号状态（每1000个周期输出一次）
        if (ITRACE_DEBUG_ENABLE && ($time % 1000 == 0)) begin
            $fwrite(debug_file, "Time: %0t - Signal Status: pe_resetn=%b, ifu_i0_valid=%b, ifu_i0_ready=%b, ifu_i1_valid=%b, ifu_i1_ready=%b\n",
                   $realtime, pe_resetn, ifu_i0_valid, ifu_i0_ready, ifu_i1_valid, ifu_i1_ready);
        end

        // 复位期间不进行统计
        if (!pe_resetn) begin
            if (ITRACE_DEBUG_ENABLE) begin
                $fwrite(debug_file, "Time: %0t - Reset Active - Clearing all state\n", $realtime);
            end

            instr_queue.delete();
            fetch_req_queue.delete();
            instr_id = 0;
            // 重置tile指令收集器
            collector_active = 0;
            collector_start_time = 0;
            collector_end_time = 0;
            collector_start_pc = 0;
            collector_fetch_time = 0;
            // 重置指令断流统计变量
            stall_monitor_start_time = 0;
            stall_monitor_end_time = 0;
            total_monitor_time = 0;
            total_stall_time = 0;
            stall_start_time = 0;
            stall_monitor_active = 0;
            stall_in_progress = 0;
            monitor_started = 0;
            total_tile_count = 0;
            // 重置功能单元利用率统计变量
            tmac_active_cycles = 0;
            tsfu_active_cycles = 0;
            talu_active_cycles = 0;
            tld_active_cycles = 0;
            tst_active_cycles = 0;
            total_monitor_cycles = 0;
            continue;
        end

        // Monitor Fetch Request Stage - 取指请求监控
        if (fetch_issue) begin
            // 处理64bit取指请求，拆分成两个32bit请求
            process_64bit_fetch_request(req_addr, $realtime);
        end

        // Monitor Fetch Return Stage - 合并处理两个通道
        // 同时考虑 ch0_active 和 ch1_active 的情况，避免收集器冲突
        // ch0_is_tile, ch1_is_tile, both_channels_active 已在文件开头通过assign语句定义

        // Debug信息 - 通道状态概览
        if (ITRACE_DEBUG_ENABLE && (ch0_active || ch1_active)) begin
            $fwrite(debug_file, "Time: %0t - Channel Status: CH0_Active=%b(Tile=%b), CH1_Active=%b(Tile=%b), Collector_Active=%b\n",
                   $realtime, ch0_active, ch0_is_tile, ch1_active, ch1_is_tile, collector_active);
        end

        // 检测0x9d02指令开始监控
        if (!monitor_started) begin
            // 检查两个通道是否有0x9d02指令
            if ((ch0_active && ifu_i0_instr[15:0] == START_OPCODE) ||
                (ch1_active && ifu_i1_instr[15:0] == START_OPCODE)) begin
                monitor_started = 1;
                stall_monitor_active = 1;
                stall_monitor_start_time = $realtime;
                if (ITRACE_DEBUG_ENABLE) begin
                    if (ch0_active && ifu_i0_instr[15:0] == START_OPCODE) begin
                        $fwrite(debug_file, "Time: %0t - Start opcode 0x%04h detected on CH0, starting stall monitoring\n",
                               $realtime, START_OPCODE);
                    end else begin
                        $fwrite(debug_file, "Time: %0t - Start opcode 0x%04h detected on CH1, starting stall monitoring\n",
                               $realtime, START_OPCODE);
                    end
                end
            end
        end

        // 检测thread_done信号，结束统计
        if (stall_monitor_active && thread_done) begin
            // 记录监控结束时间并计算总监控时间
            stall_monitor_end_time = $realtime;
            total_monitor_time = stall_monitor_end_time - stall_monitor_start_time;

            // 如果当前还在断流中，结束断流统计
            if (stall_in_progress) begin
                total_stall_time = total_stall_time + ($realtime - stall_start_time);
                stall_in_progress = 0;
            end
            stall_monitor_active = 0;
            if (ITRACE_DEBUG_ENABLE) begin
                $fwrite(debug_file, "Time: %0t - Thread done signal detected, stopping stall monitoring\n", $realtime);
                $fwrite(debug_file, "  Monitor start time: %.2f\n", stall_monitor_start_time);
                $fwrite(debug_file, "  Monitor end time: %.2f\n", stall_monitor_end_time);
                $fwrite(debug_file, "  Total monitor time: %.2f\n", total_monitor_time);
                $fwrite(debug_file, "  Total stall time: %.2f\n", total_stall_time);
            end
        end

        // 指令断流统计逻辑
        if (stall_monitor_active) begin
            // 在监控期间，检测断流状态
            if (instruction_stall && !stall_in_progress) begin
                // 开始断流
                stall_in_progress = 1;
                stall_start_time = $realtime;
                if (ITRACE_DEBUG_ENABLE) begin
                    $fwrite(debug_file, "Time: %0t - Instruction stall started\n", $realtime);
                end
            end else if (!instruction_stall && stall_in_progress) begin
                // 断流结束
                stall_in_progress = 0;
                total_stall_time = total_stall_time + ($realtime - stall_start_time);
                if (ITRACE_DEBUG_ENABLE) begin
                    $fwrite(debug_file, "Time: %0t - Instruction stall ended, duration: %.2f, total_stall: %.2f\n",
                           $realtime, $realtime - stall_start_time, total_stall_time);
                end
            end

            // 功能单元利用率统计逻辑 - 每个时钟周期累加
            total_monitor_cycles++;

            // 统计各功能单元的活跃周期
            if (tmac_executing) tmac_active_cycles++;
            if (tsfu_executing) tsfu_active_cycles++;
            if (talu_executing) talu_active_cycles++;
            if (tld_executing) tld_active_cycles++;
            if (tst_executing) tst_active_cycles++;

            // Debug信息 - 功能单元状态（每1000个周期输出一次）
            if (ITRACE_DEBUG_ENABLE && (total_monitor_cycles % 1000 == 0)) begin
                $fwrite(debug_file, "Time: %0t - FU Status: TMAC=%b, TSFU=%b, TALU=%b, TLD=%b, TST=%b, Cycles=%0d\n",
                       $realtime, tmac_executing, tsfu_executing, talu_executing, tld_executing, tst_executing, total_monitor_cycles);
            end
        end

        // 处理sync/wait指令 - 这些指令在fetch后直接丢弃，不进入后续pipeline
        // 但先将信息写入itrace.log文件，显示fetch和decode start时间
        if (ch0_is_sync_wait || ch1_is_sync_wait) begin
            if (ch0_is_sync_wait) begin
                // 处理CH0的sync/wait指令
                logic [47:0] fetch_pc_48bit;
                realtime fetch_time_ch0;
                string disasm_str;
                realtime fetch_start_time;
                realtime decode_start_time;
                logic [47:0] pc_to_use;

                find_and_remove_fetch_info(ifu_i0_pc[31:0], fetch_pc_48bit, fetch_time_ch0);

                // 创建sync/wait指令记录并立即写入文件
                disasm_str = disassemble_instruction({96'h0, ifu_i0_instr}, INSTR_32BIT);
                fetch_start_time = (fetch_time_ch0 > 0) ? fetch_time_ch0 : $realtime;
                decode_start_time = $realtime;
                pc_to_use = (fetch_pc_48bit != 48'h0) ? fetch_pc_48bit : ifu_i0_pc;

                // 直接写入itrace.log文件 - sync/wait指令只有fetch和decode start时间
                instr_id++;
                $fwrite(perf_file, "%0d,0x%012h,0x%032h,\"%s\",%0d,%0d,0,0,0\n",
                       instr_id, pc_to_use, {96'h0, ifu_i0_instr}, disasm_str,
                       int'(fetch_start_time), int'(decode_start_time));

                if (ITRACE_DEBUG_ENABLE) begin
                    if (fetch_time_ch0 > 0) begin
                        $fwrite(debug_file, "Time: %0t - CH0 Sync/Wait Written to Log: PC=0x%012h, Instr=0x%08h, Disasm=\"%s\", FetchTime=%.2f\n",
                               $realtime, pc_to_use, ifu_i0_instr, disasm_str, fetch_time_ch0);
                    end else begin
                        $fwrite(debug_file, "Time: %0t - CH0 Sync/Wait Written to Log: PC=0x%012h, Instr=0x%08h, Disasm=\"%s\", No fetch req found\n",
                               $realtime, pc_to_use, ifu_i0_instr, disasm_str);
                    end
                end
            end

            if (ch1_is_sync_wait) begin
                // 处理CH1的sync/wait指令
                logic [47:0] fetch_pc_48bit;
                realtime fetch_time_ch1;
                string disasm_str;
                realtime fetch_start_time;
                realtime decode_start_time;
                logic [47:0] pc_to_use;

                find_and_remove_fetch_info(ifu_i1_pc[31:0], fetch_pc_48bit, fetch_time_ch1);

                // 创建sync/wait指令记录并立即写入文件
                disasm_str = disassemble_instruction({96'h0, ifu_i1_instr}, INSTR_32BIT);
                fetch_start_time = (fetch_time_ch1 > 0) ? fetch_time_ch1 : $realtime;
                decode_start_time = $realtime;
                pc_to_use = (fetch_pc_48bit != 48'h0) ? fetch_pc_48bit : ifu_i1_pc;

                // 直接写入itrace.log文件 - sync/wait指令只有fetch和decode start时间
                instr_id++;
                $fwrite(perf_file, "%0d,0x%012h,0x%032h,\"%s\",%0d,%0d,0,0,0\n",
                       instr_id, pc_to_use, {96'h0, ifu_i1_instr}, disasm_str,
                       int'(fetch_start_time), int'(decode_start_time));

                if (ITRACE_DEBUG_ENABLE) begin
                    if (fetch_time_ch1 > 0) begin
                        $fwrite(debug_file, "Time: %0t - CH1 Sync/Wait Written to Log: PC=0x%012h, Instr=0x%08h, Disasm=\"%s\", FetchTime=%.2f\n",
                               $realtime, pc_to_use, ifu_i1_instr, disasm_str, fetch_time_ch1);
                    end else begin
                        $fwrite(debug_file, "Time: %0t - CH1 Sync/Wait Written to Log: PC=0x%012h, Instr=0x%08h, Disasm=\"%s\", No fetch req found\n",
                               $realtime, pc_to_use, ifu_i1_instr, disasm_str);
                    end
                end
            end
        end else begin
            // 处理tile指令收集的主要逻辑
            if (!collector_active) begin
            // 收集器未激活，检查是否需要开始新的收集
            if (ch0_is_tile && ch1_is_tile && both_channels_active) begin
                // 两个通道同时有tile指令 - 优先处理CH0，CH1作为第二个字
                if (ITRACE_DEBUG_ENABLE) begin
                    $fwrite(debug_file, "Time: %0t - Both Channels Tile: CH0=0x%08h@0x%012h, CH1=0x%08h@0x%012h\n",
                           $realtime, ifu_i0_instr, ifu_i0_pc, ifu_i1_instr, ifu_i1_pc);
                end

                // 用CH0开始收集
                active_collector = init_collector(ifu_i0_instr);
                collector_active = 1;
                collector_start_time = $realtime;
                // 从取指请求队列中获取48位PC（取指请求时的PC）
                find_and_remove_fetch_info(ifu_i0_pc[31:0], collector_start_pc, collector_fetch_time);



                // 如果CH0是32位指令，立即完成
                if (active_collector.is_complete) begin
                    instr_perf_t new_instr;

                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - CH0 32-bit Complete (Both Active): PC=0x%012h, Data=0x%032h\n",
                               $realtime, collector_start_pc, active_collector.instruction_data);
                    end

                    new_instr.pc = collector_start_pc;
                    new_instr.instr = 128'h0;
                    new_instr.instr = active_collector.instruction_data;
                    new_instr.disasm_str = disassemble_instruction(active_collector.instruction_data, active_collector.expected_length);
                    // 使用已经获取的取指时间（在find_and_remove_fetch_info中获取）
                    new_instr.fetch_start = (collector_fetch_time > 0) ? collector_fetch_time : collector_start_time;
                    new_instr.fetch_end = $realtime;
                    new_instr.decode_start = $realtime;  // decode开始时间 = 取指完成时间
                    new_instr.decode_end = 0;  // decode结束时间将在dispatch时设置
                    new_instr.valid = 1;

                    instr_queue.push_back(new_instr);

                    // 统计tile指令数量
                    total_tile_count++;

                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - CH0 32-bit Instruction Added to Queue: PC=0x%012h\n",
                               $realtime, collector_start_pc);
                    end

                    // 重置收集器，然后处理CH1
                    collector_active = 0;

                    // 立即开始CH1的收集
                    active_collector = init_collector(ifu_i1_instr);
                    collector_active = 1;
                    collector_start_time = $realtime;
                    // 从取指请求队列中获取48位PC（取指请求时的PC）
                    find_and_remove_fetch_info(ifu_i1_pc[31:0], collector_start_pc, fetch_req_time_ch1);



                    // 如果CH1也是32位指令，立即完成
                    if (active_collector.is_complete) begin
                        instr_perf_t new_instr_ch1;

                        if (ITRACE_DEBUG_ENABLE) begin
                            $fwrite(debug_file, "Time: %0t - CH1 32-bit Complete (Both Active): PC=0x%012h, Data=0x%032h\n",
                                   $realtime, collector_start_pc, active_collector.instruction_data);
                        end

                        new_instr_ch1.pc = collector_start_pc;
                        new_instr_ch1.instr = 128'h0;
                        new_instr_ch1.instr = active_collector.instruction_data;
                        new_instr_ch1.disasm_str = disassemble_instruction(active_collector.instruction_data, active_collector.expected_length);
                        // 使用已经获取的取指时间（在find_and_remove_fetch_info中获取）
                        new_instr_ch1.fetch_start = (fetch_req_time_ch1 > 0) ? fetch_req_time_ch1 : collector_start_time;
                        new_instr_ch1.fetch_end = $realtime;
                        new_instr_ch1.decode_start = $realtime;  // decode开始时间 = 取指完成时间
                        new_instr_ch1.decode_end = 0;  // decode结束时间将在dispatch时设置
                        new_instr_ch1.valid = 1;

                        instr_queue.push_back(new_instr_ch1);

                        // 统计tile指令数量
                        total_tile_count++;

                        if (ITRACE_DEBUG_ENABLE) begin
                            $fwrite(debug_file, "Time: %0t - CH1 32-bit Instruction Added to Queue: PC=0x%012h\n",
                                   $realtime, collector_start_pc);
                        end

                        collector_active = 0;
                    end
                end else begin
                    // CH0是多字指令，尝试用CH1作为第二个字
                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - CH0 Multi-word, Adding CH1 as Second Word: CH1=0x%08h\n",
                               $realtime, ifu_i1_instr);
                    end

                    active_collector = add_word_to_collector(active_collector, ifu_i1_instr);
                    collector_end_time = $realtime;

                    // 检查是否完成
                    if (active_collector.is_complete) begin
                        instr_perf_t new_instr;

                        if (ITRACE_DEBUG_ENABLE) begin
                            $fwrite(debug_file, "Time: %0t - Multi-word Complete (Both Channels): PC=0x%012h, Length=%0d, Data=0x%032h\n",
                                   $realtime, collector_start_pc, active_collector.expected_length, active_collector.instruction_data);
                        end

                        new_instr.pc = collector_start_pc;
                        new_instr.instr = 128'h0;
                        new_instr.instr = active_collector.instruction_data;
                        new_instr.disasm_str = disassemble_instruction(active_collector.instruction_data, active_collector.expected_length);
                        // 使用已经获取的取指时间（在开始收集时获取）
                        new_instr.fetch_start = (collector_fetch_time > 0) ? collector_fetch_time : collector_start_time;
                        new_instr.fetch_end = collector_end_time;
                        new_instr.decode_start = collector_end_time;  // decode开始时间 = 取指完成时间
                        new_instr.decode_end = 0;  // decode结束时间将在dispatch时设置
                        new_instr.valid = 1;

                        instr_queue.push_back(new_instr);

                        // 统计tile指令数量
                        total_tile_count++;

                        if (ITRACE_DEBUG_ENABLE) begin
                            $fwrite(debug_file, "Time: %0t - Multi-word Instruction Added to Queue: PC=0x%012h\n",
                                   $realtime, collector_start_pc);
                        end

                        collector_active = 0;
                    end
                end
            end else if (ch0_is_tile) begin
                // 只有CH0有tile指令
                if (ITRACE_DEBUG_ENABLE) begin
                    $fwrite(debug_file, "Time: %0t - CH0 Only Tile: PC=0x%012h, Instr=0x%08h\n",
                           $realtime, ifu_i0_pc, ifu_i0_instr);
                end

                active_collector = init_collector(ifu_i0_instr);
                collector_active = 1;
                collector_start_time = $realtime;
                // 从取指请求队列中获取48位PC（取指请求时的PC）
                find_and_remove_fetch_info(ifu_i0_pc[31:0], collector_start_pc, collector_fetch_time);



                // 如果是32位指令，立即完成
                if (active_collector.is_complete) begin
                    instr_perf_t new_instr;

                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - CH0 32-bit Complete: PC=0x%012h, Data=0x%032h\n",
                               $realtime, collector_start_pc, active_collector.instruction_data);
                    end

                    new_instr.pc = collector_start_pc;
                    new_instr.instr = 128'h0;
                    new_instr.instr = active_collector.instruction_data;
                    new_instr.disasm_str = disassemble_instruction(active_collector.instruction_data, active_collector.expected_length);
                    // 使用已经获取的取指时间（在find_and_remove_fetch_info中获取）
                    new_instr.fetch_start = (collector_fetch_time > 0) ? collector_fetch_time : collector_start_time;
                    new_instr.fetch_end = $realtime;
                    new_instr.decode_start = $realtime;  // decode开始时间 = 取指完成时间
                    new_instr.decode_end = 0;  // decode结束时间将在dispatch时设置
                    new_instr.valid = 1;

                    instr_queue.push_back(new_instr);

                    // 统计tile指令数量
                    total_tile_count++;

                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - CH0 Only 32-bit Instruction Added to Queue: PC=0x%012h\n",
                               $realtime, collector_start_pc);
                    end

                    collector_active = 0;
                end
            end else if (ch1_is_tile) begin
                // 只有CH1有tile指令
                if (ITRACE_DEBUG_ENABLE) begin
                    $fwrite(debug_file, "Time: %0t - CH1 Only Tile: PC=0x%012h, Instr=0x%08h\n",
                           $realtime, ifu_i1_pc, ifu_i1_instr);
                end

                active_collector = init_collector(ifu_i1_instr);
                collector_active = 1;
                collector_start_time = $realtime;
                // 从取指请求队列中获取48位PC（取指请求时的PC）
                find_and_remove_fetch_info(ifu_i1_pc[31:0], collector_start_pc, collector_fetch_time);



                // 如果是32位指令，立即完成
                if (active_collector.is_complete) begin
                    instr_perf_t new_instr;

                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - CH1 32-bit Complete: PC=0x%012h, Data=0x%032h\n",
                               $realtime, collector_start_pc, active_collector.instruction_data);
                    end

                    new_instr.pc = collector_start_pc;
                    new_instr.instr = 128'h0;
                    new_instr.instr = active_collector.instruction_data;
                    new_instr.disasm_str = disassemble_instruction(active_collector.instruction_data, active_collector.expected_length);
                    // 使用已经获取的取指时间（在find_and_remove_fetch_info中获取）
                    new_instr.fetch_start = (collector_fetch_time > 0) ? collector_fetch_time : collector_start_time;
                    new_instr.fetch_end = $realtime;
                    new_instr.decode_start = $realtime;  // decode开始时间 = 取指完成时间
                    new_instr.decode_end = 0;  // decode结束时间将在dispatch时设置
                    new_instr.valid = 1;

                    instr_queue.push_back(new_instr);

                    // 统计tile指令数量
                    total_tile_count++;

                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - CH1 Only 32-bit Instruction Added to Queue: PC=0x%012h\n",
                               $realtime, collector_start_pc);
                    end

                    collector_active = 0;
                end
            end else begin
                // 处理非tile指令的时间跟踪
                if (ch0_active) begin
                    // 查询并删除对应的取指请求（如果存在）
                    fetch_req_time_ch0 = find_and_remove_fetch_time(ifu_i0_pc);

                    if (ITRACE_DEBUG_ENABLE) begin
                        if (fetch_req_time_ch0 > 0) begin
                            $fwrite(debug_file, "Time: %0t - CH0 Non-Tile Discarded: PC=0x%012h, Instr=0x%08h, Removed fetch req (time=%.2f)\n",
                                   $realtime, ifu_i0_pc, ifu_i0_instr, fetch_req_time_ch0);
                        end else begin
                            $fwrite(debug_file, "Time: %0t - CH0 Non-Tile Discarded: PC=0x%012h, Instr=0x%08h, No fetch req found\n",
                                   $realtime, ifu_i0_pc, ifu_i0_instr);
                        end
                    end


                end

                if (ch1_active) begin
                    // 查询并删除对应的取指请求（如果存在）
                    fetch_req_time_ch1 = find_and_remove_fetch_time(ifu_i1_pc);

                    if (ITRACE_DEBUG_ENABLE) begin
                        if (fetch_req_time_ch1 > 0) begin
                            $fwrite(debug_file, "Time: %0t - CH1 Non-Tile Discarded: PC=0x%012h, Instr=0x%08h, Removed fetch req (time=%.2f)\n",
                                   $realtime, ifu_i1_pc, ifu_i1_instr, fetch_req_time_ch1);
                        end else begin
                            $fwrite(debug_file, "Time: %0t - CH1 Non-Tile Discarded: PC=0x%012h, Instr=0x%08h, No fetch req found\n",
                                   $realtime, ifu_i1_pc, ifu_i1_instr);
                        end
                    end


                end
            end
        end else begin
            // 收集器已激活，继续收集后续字
            if (ch0_active || ch1_active) begin
                logic [31:0] next_word;
                logic [47:0] next_pc;

                // 优先使用CH0，如果CH0不活跃则使用CH1
                if (ch0_active) begin
                    next_word = ifu_i0_instr;
                    next_pc = ifu_i0_pc;

                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - Continuing Collection with CH0: PC=0x%012h, Word=0x%08h, Words_Collected=%b\n",
                               $realtime, next_pc, next_word, active_collector.collected_words);
                    end
                end else begin
                    next_word = ifu_i1_instr;
                    next_pc = ifu_i1_pc;

                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - Continuing Collection with CH1: PC=0x%012h, Word=0x%08h, Words_Collected=%b\n",
                               $realtime, next_pc, next_word, active_collector.collected_words);
                    end
                end

                active_collector = add_word_to_collector(active_collector, next_word);
                collector_end_time = $realtime;

                // Debug信息 - 收集状态更新
                if (ITRACE_DEBUG_ENABLE) begin
                    $fwrite(debug_file, "Time: %0t - Collection Updated: Words_Collected=%b, Is_Complete=%b\n",
                           $realtime, active_collector.collected_words, active_collector.is_complete);
                end

                // 检查指令是否收集完成
                if (active_collector.is_complete) begin
                    instr_perf_t new_instr;

                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - Multi-word Tile Instruction Complete: PC=0x%012h, Length=%0d, Data=0x%032h\n",
                               $realtime, collector_start_pc, active_collector.expected_length, active_collector.instruction_data);
                    end

                    new_instr.pc = collector_start_pc;
                    new_instr.instr = 128'h0;
                    new_instr.instr = active_collector.instruction_data;
                    new_instr.disasm_str = disassemble_instruction(active_collector.instruction_data, active_collector.expected_length);
                    // 使用已经获取的取指时间（在开始收集时获取）
                    new_instr.fetch_start = (collector_fetch_time > 0) ? collector_fetch_time : collector_start_time;
                    new_instr.fetch_end = collector_end_time;
                    new_instr.decode_start = collector_end_time;  // decode开始时间 = 取指完成时间
                    new_instr.decode_end = 0;  // decode结束时间将在dispatch时设置
                    new_instr.valid = 1;

                    instr_queue.push_back(new_instr);

                    // 统计tile指令数量
                    total_tile_count++;

                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - Multi-word Instruction Added to Queue: PC=0x%012h\n",
                               $realtime, collector_start_pc);
                    end

                    collector_active = 0;
                end
            end
        end
        end // 结束sync/wait指令处理的else块

        // Monitor Dispatch Stage - 指令开始dispatch (端口0)
        if (idu_isq_uop0_valid) begin
            // Debug信息 - dispatch端口0
            if (ITRACE_DEBUG_ENABLE) begin
                $fwrite(debug_file, "Time: %0t - Dispatch Port0: Valid, Queue_Size=%0d\n",
                       $realtime, instr_queue.size());
            end

            // 查找对应的已取回指令并更新dispatch信息
            for (int i = 0; i < instr_queue.size(); i++) begin
                if (instr_queue[i].dispatch_start == 0 && instr_queue[i].fetch_end != 0) begin
                    // Debug信息 - 找到匹配指令
                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - Dispatch Port0 Match: Index=%0d, PC=0x%012h, TInstr=0x%013h, ITag=0x%08h\n",
                               $realtime, i, instr_queue[i].pc, idu_isq_uop0_opcode, ctl_isq_uop0_itag);
                    end

                    // 更新dispatch阶段的tinstr和itag信息
                    instr_queue[i].tinstr = idu_isq_uop0_opcode;
                    instr_queue[i].itag = ctl_isq_uop0_itag;
                    instr_queue[i].decode_end = $realtime;  // decode结束时间 = dispatch开始时间
                    instr_queue[i].dispatch_start = $realtime;
                    instr_id++;
                    break;
                end
            end
        end

        // Monitor Dispatch Stage - 指令开始dispatch (端口1)
        if (idu_isq_uop1_valid) begin
            // Debug信息 - dispatch端口1
            if (ITRACE_DEBUG_ENABLE) begin
                $fwrite(debug_file, "Time: %0t - Dispatch Port1: Valid, Queue_Size=%0d\n",
                       $realtime, instr_queue.size());
            end

            // 查找对应的已取回指令并更新dispatch信息
            for (int i = 0; i < instr_queue.size(); i++) begin
                if (instr_queue[i].dispatch_start == 0 && instr_queue[i].fetch_end != 0) begin
                    // Debug信息 - 找到匹配指令
                    if (ITRACE_DEBUG_ENABLE) begin
                        $fwrite(debug_file, "Time: %0t - Dispatch Port1 Match: Index=%0d, PC=0x%012h, TInstr=0x%013h, ITag=0x%08h\n",
                               $realtime, i, instr_queue[i].pc, idu_isq_uop1_opcode, ctl_isq_uop1_itag);
                    end

                    // 更新dispatch阶段的tinstr和itag信息
                    instr_queue[i].tinstr = idu_isq_uop1_opcode;
                    instr_queue[i].itag = ctl_isq_uop1_itag;
                    instr_queue[i].decode_end = $realtime;  // decode结束时间 = dispatch开始时间
                    instr_queue[i].dispatch_start = $realtime;
                    instr_id++;
                    break;
                end
            end
        end
        
        // 更新队列中所有指令的状态
        for (int i = 0; i < instr_queue.size(); i++) begin
            // Execute开始 - 通过匹配itag来识别具体指令
            if (instr_queue[i].execute_start == 0 && instr_queue[i].dispatch_end == 0) begin
                // 检查各个执行单元的itag是否匹配当前指令
                if ((`TCORE.tc_tfe.isq.isq_opc_tmac_init_v &&
                     `TCORE.tc_tfe.isq.isq_opc_tmac_itag === instr_queue[i].itag) ||
                    (`TCORE.tc_tfe.isq.isq_opc_talu_init_v &&
                     `TCORE.tc_tfe.isq.isq_opc_talu_itag === instr_queue[i].itag) ||
                    (`TCORE.tc_tfe.isq.isq_opc_tlsu_init_ld_v &&
                     `TCORE.tc_tfe.isq.isq_opc_tlsu_itag_ld) === instr_queue[i].itag ||
                    (`TCORE.tc_tfe.isq.isq_opc_tlsu_init_st_v &&
                     `TCORE.tc_tfe.isq.isq_opc_tlsu_itag_st) === instr_queue[i].itag ||                     
                    (`TCORE.tc_tfe.isq.isq_opc_tsfu_init_v &&
                     `TCORE.tc_tfe.isq.isq_opc_tsfu_itag === instr_queue[i].itag) ||
                    (`TCORE.tc_tfe.isq.isq_opc_tdte_init_v &&
                     `TCORE.tc_tfe.isq.isq_opc_tdte_itag === instr_queue[i].itag)) begin
                    instr_queue[i].dispatch_end = $realtime;
                    instr_queue[i].execute_start = instr_queue[i].dispatch_end;
                end
            end

            // Execute完成 - 检查finish信号和itag匹配
            if (instr_queue[i].execute_end == 0 && instr_queue[i].dispatch_end != 0) begin
                // 检查需要写回到TRF的指令完成信号
                if ((`TCORE.tc_tfe.cmt.tlsu_isq_ltag_fin_v_t0 &&
                     `TCORE.tc_tfe.cmt.tlsu_isq_ltag_fin_itag_t0 === instr_queue[i].itag) ||
                    (`TCORE.tc_tfe.cmt.tlsu_isq_ltag_fin_v_t1 &&
                     `TCORE.tc_tfe.cmt.tlsu_isq_ltag_fin_itag_t1 === instr_queue[i].itag) ||
                    (`TCORE.tc_tfe.cmt.tmac_isq_fin_valid &&
                     `TCORE.tc_tfe.cmt.tmac_isq_fin_itag === instr_queue[i].itag) ||
                    // 检查不需要写回到TRF的指令完成信号
                    (`TCORE.tc_tfe.cmt.tlsu_isq_stag_fin_v_t0 &&
                     `TCORE.tc_tfe.cmt.tlsu_isq_stag_fin_itag_t0 === instr_queue[i].itag) ||
                    (`TCORE.tc_tfe.cmt.tlsu_isq_stag_fin_v_t1 &&
                     `TCORE.tc_tfe.cmt.tlsu_isq_stag_fin_itag_t1 === instr_queue[i].itag) ||
                    (`TCORE.tc_tfe.cmt.tdte_isq_fin_valid &&
                     `TCORE.tc_tfe.cmt.tdte_isq_fin_itag === instr_queue[i].itag) ||
                    (`TCORE.tc_tfe.cmt.tmv_isq_fin_valid &&
                     `TCORE.tc_tfe.cmt.tmv_isq_fin_itag === instr_queue[i].itag) ||
                    (`TCORE.tc_tfe.cmt.opc_tmac_fast_fin_v &&
                     `TCORE.tc_tfe.cmt.opc_tmac_fast_fin_itag === instr_queue[i].itag)) begin

                    instr_queue[i].execute_end = $realtime;

                    // 输出log格式：包含ID、PC、指令、反编译字符串和时间信息（时间为整数）
                    $fwrite(perf_file, "%0d,0x%012h,0x%032h,\"%s\",%0d,%0d,%0d,%0d,%0d\n",
                           instr_id, instr_queue[i].pc, instr_queue[i].instr, instr_queue[i].disasm_str,
                           int'(instr_queue[i].fetch_start), int'(instr_queue[i].decode_start), int'(instr_queue[i].dispatch_start),
                           int'(instr_queue[i].execute_start), int'(instr_queue[i].execute_end));

                    // 从队列中移除已完成的指令
                    instr_queue.delete(i);
                    i--;
                end
            end
        end
    end
end

// Simulation end cleanup
final begin
    // 检查是否有未完成的指令
    if (instr_queue.size() > 0) begin
        $fwrite(perf_file, "\n// ERROR: %0d instructions were not completed at simulation end:\n", instr_queue.size());
        //$display("ERROR: %0d instructions were not completed at simulation end", instr_queue.size());

        for (int i = 0; i < instr_queue.size(); i++) begin
            $fwrite(perf_file, "// Incomplete Instr: ID=%0d, PC=0x%012h, Instr=0x%032h, Disasm=\"%s\"",
                   i, instr_queue[i].pc, instr_queue[i].instr, instr_queue[i].disasm_str);

            if (instr_queue[i].fetch_end == 0) begin
                $fwrite(perf_file, " - STUCK in FETCH stage\n");
            end else if (instr_queue[i].dispatch_start == 0) begin
                $fwrite(perf_file, " - STUCK between FETCH and DISPATCH\n");
            end else if (instr_queue[i].dispatch_end == 0) begin
                $fwrite(perf_file, " - STUCK in DISPATCH stage\n");
            end else if (instr_queue[i].execute_start == 0) begin
                $fwrite(perf_file, " - STUCK between DISPATCH and EXECUTE\n");
            end else if (instr_queue[i].execute_end == 0) begin
                $fwrite(perf_file, " - STUCK in EXECUTE stage\n");
            end else begin
                $fwrite(perf_file, " - UNKNOWN state\n");
            end
        end
        $fwrite(perf_file, "\n");
    end else begin
        $fwrite(perf_file, "\n// All instructions completed successfully\n");
        //$display("All instructions completed successfully");
    end

    // 输出指令断流统计信息
    if (monitor_started) begin
        real stall_percentage = 0.0;
        real tmac_utilization = 0.0;
        real tsfu_utilization = 0.0;
        real talu_utilization = 0.0;
        real tld_utilization = 0.0;
        real tst_utilization = 0.0;

        // 计算断流时间百分比
        if (total_monitor_time > 0) begin
            stall_percentage = (total_stall_time * 100.0) / total_monitor_time;
        end

        // 计算功能单元利用率
        if (total_monitor_cycles > 0) begin
            tmac_utilization = (tmac_active_cycles * 100.0) / total_monitor_cycles;
            tsfu_utilization = (tsfu_active_cycles * 100.0) / total_monitor_cycles;
            talu_utilization = (talu_active_cycles * 100.0) / total_monitor_cycles;
            tld_utilization = (tld_active_cycles * 100.0) / total_monitor_cycles;
            tst_utilization = (tst_active_cycles * 100.0) / total_monitor_cycles;
        end

        $fwrite(perf_file, "\n// ===== INSTRUCTION STALL STATISTICS =====\n");
        $fwrite(perf_file, "// Stall monitor start time (0x9d02 fetch): %.2f\n", stall_monitor_start_time);
        $fwrite(perf_file, "// Stall monitor end time (thread_done): %.2f\n", stall_monitor_end_time);
        $fwrite(perf_file, "// Total monitor time: %.2f\n", total_monitor_time);
        $fwrite(perf_file, "// Total stall time: %.2f\n", total_stall_time);
        $fwrite(perf_file, "// Stall percentage: %.2f%%\n", stall_percentage);
        $fwrite(perf_file, "// Total tile instructions: %0d\n", total_tile_count);

        $fwrite(perf_file, "\n// ===== FUNCTIONAL UNIT UTILIZATION STATISTICS =====\n");
        $fwrite(perf_file, "// Total monitor cycles: %0d\n", total_monitor_cycles);
        $fwrite(perf_file, "// TMAC utilization: %.2f%% (%0d/%0d cycles)\n", tmac_utilization, tmac_active_cycles, total_monitor_cycles);
        $fwrite(perf_file, "// TSFU utilization: %.2f%% (%0d/%0d cycles)\n", tsfu_utilization, tsfu_active_cycles, total_monitor_cycles);
        $fwrite(perf_file, "// TALU utilization: %.2f%% (%0d/%0d cycles)\n", talu_utilization, talu_active_cycles, total_monitor_cycles);
        $fwrite(perf_file, "// TLD utilization: %.2f%% (%0d/%0d cycles)\n", tld_utilization, tld_active_cycles, total_monitor_cycles);
        $fwrite(perf_file, "// TST utilization: %.2f%% (%0d/%0d cycles)\n", tst_utilization, tst_active_cycles, total_monitor_cycles);

        // 添加调试信息
        if (ITRACE_DEBUG_ENABLE) begin
            $fwrite(debug_file, "Time: %0t - FINAL STATISTICS:\n", $realtime);
            $fwrite(debug_file, "  monitor_started: %b\n", monitor_started);
            $fwrite(debug_file, "  stall_monitor_start_time: %.2f\n", stall_monitor_start_time);
            $fwrite(debug_file, "  stall_monitor_end_time: %.2f\n", stall_monitor_end_time);
            $fwrite(debug_file, "  total_monitor_time: %.2f\n", total_monitor_time);
            $fwrite(debug_file, "  total_stall_time: %.2f\n", total_stall_time);
            $fwrite(debug_file, "  stall_percentage: %.2f%%\n", stall_percentage);
            $fwrite(debug_file, "  total_tile_count: %0d\n", total_tile_count);
            $fwrite(debug_file, "  total_monitor_cycles: %0d\n", total_monitor_cycles);
            $fwrite(debug_file, "  tmac_utilization: %.2f%%\n", tmac_utilization);
            $fwrite(debug_file, "  tsfu_utilization: %.2f%%\n", tsfu_utilization);
            $fwrite(debug_file, "  talu_utilization: %.2f%%\n", talu_utilization);
            $fwrite(debug_file, "  tld_utilization: %.2f%%\n", tld_utilization);
            $fwrite(debug_file, "  tst_utilization: %.2f%%\n", tst_utilization);
        end

        //$display("=== INSTRUCTION STALL STATISTICS ===");
        //$display("Stall monitor start time (0x9d02 fetch): %.2f", stall_monitor_start_time);
        //$display("Stall monitor end time (thread_done): %.2f", stall_monitor_end_time);
        //$display("Total monitor time: %.2f", total_monitor_time);
        //$display("Total stall time: %.2f", total_stall_time);
        //$display("Stall percentage: %.2f%%", stall_percentage);
        //$display("Total tile instructions: %0d", total_tile_count);
//
        //$display("=== FUNCTIONAL UNIT UTILIZATION STATISTICS ===");
        //$display("Total monitor cycles: %0d", total_monitor_cycles);
        //$display("TMAC utilization: %.2f%% (%0d/%0d cycles)", tmac_utilization, tmac_active_cycles, total_monitor_cycles);
        //$display("TSFU utilization: %.2f%% (%0d/%0d cycles)", tsfu_utilization, tsfu_active_cycles, total_monitor_cycles);
        //$display("TALU utilization: %.2f%% (%0d/%0d cycles)", talu_utilization, talu_active_cycles, total_monitor_cycles);
        //$display("TLD utilization: %.2f%% (%0d/%0d cycles)", tld_utilization, tld_active_cycles, total_monitor_cycles);
        //$display("TST utilization: %.2f%% (%0d/%0d cycles)", tst_utilization, tst_active_cycles, total_monitor_cycles);
    end else begin
        $fwrite(perf_file, "\n// Start opcode 0x9d02 not found - no stall statistics available\n");
        //$display("Start opcode 0x9d02 not found - no stall statistics available");
    end

    $fclose(perf_file);
    if (ITRACE_DEBUG_ENABLE) begin
        $fwrite(debug_file, "Time: %0t - Performance monitoring complete\n", $realtime);
        $fclose(debug_file);
    end
    //$display("Performance monitoring complete. Results saved to itrace.log");
end

// Final block - 仿真结束时的清理工作
final begin
    // 详细的未完成指令分析
    check_incomplete_instructions();

    if (ITRACE_DEBUG_ENABLE) begin
        $fwrite(debug_file, "Time: %0t - Final cleanup completed\n", $realtime);
        $fclose(debug_file);
    end

    //$display("Performance monitor final cleanup completed");
end

// 详细的未完成指令检查函数
function automatic void check_incomplete_instructions();
    integer final_file;
    integer incomplete_count = 0;
    integer stuck_in_fetch = 0;
    integer stuck_in_decode = 0; 
    integer stuck_in_dispatch = 0;
    integer stuck_in_execute = 0;
    integer fetch_req_count = 0;
    realtime current_time;
    
    current_time = $realtime;
    
    if (instr_queue.size() > 0) begin
        //$display("Warning: %0d incomplete instructions found at simulation end", instr_queue.size());
        
        final_file = $fopen("incomplete_instructions.log", "w");
        $fwrite(final_file, "=== INCOMPLETE INSTRUCTIONS ANALYSIS ===\n");
        $fwrite(final_file, "Simulation End Time: %.2f\n", current_time);
        $fwrite(final_file, "Total Incomplete Instructions: %0d\n\n", instr_queue.size());
        
        // 分析每个未完成的指令
        for (int i = 0; i < instr_queue.size(); i++) begin
            if (instr_queue[i].valid) begin
                // 声明块内变量
                realtime stuck_duration;
                string stage_name;
                string detailed_analysis;
                
                incomplete_count++;
                stuck_duration = 0;
                stage_name = "";
                detailed_analysis = "";
                
                // 判断指令卡在哪个阶段
                if (instr_queue[i].fetch_end == 0) begin
                    stuck_in_fetch++;
                    stage_name = "FETCH";
                    stuck_duration = current_time - instr_queue[i].fetch_start;
                    detailed_analysis = $sformatf("Fetch started at %.2f, stuck for %.2f cycles", 
                                                instr_queue[i].fetch_start, stuck_duration);
                end else if (instr_queue[i].decode_start == 0) begin
                    stuck_in_decode++;
                    stage_name = "DECODE_WAIT";
                    stuck_duration = current_time - instr_queue[i].fetch_end;
                    detailed_analysis = $sformatf("Fetch completed at %.2f, waiting for decode for %.2f cycles",
                                                instr_queue[i].fetch_end, stuck_duration);
                end else if (instr_queue[i].dispatch_start == 0) begin
                    stuck_in_decode++;
                    stage_name = "DECODE";
                    stuck_duration = current_time - instr_queue[i].decode_start;
                    detailed_analysis = $sformatf("Decode started at %.2f, stuck for %.2f cycles",
                                                instr_queue[i].decode_start, stuck_duration);
                end else if (instr_queue[i].dispatch_end == 0) begin
                    stuck_in_dispatch++;
                    stage_name = "DISPATCH";
                    stuck_duration = current_time - instr_queue[i].dispatch_start;
                    detailed_analysis = $sformatf("Dispatch started at %.2f, stuck for %.2f cycles",
                                                instr_queue[i].dispatch_start, stuck_duration);
                end else if (instr_queue[i].execute_start == 0) begin
                    stuck_in_execute++;
                    stage_name = "EXECUTE_WAIT";
                    stuck_duration = current_time - instr_queue[i].dispatch_end;
                    detailed_analysis = $sformatf("Dispatch completed at %.2f, waiting for execute for %.2f cycles",
                                                instr_queue[i].dispatch_end, stuck_duration);
                end else if (instr_queue[i].execute_end == 0) begin
                    stuck_in_execute++;
                    stage_name = "EXECUTE";
                    stuck_duration = current_time - instr_queue[i].execute_start;
                    detailed_analysis = $sformatf("Execute started at %.2f, stuck for %.2f cycles",
                                                instr_queue[i].execute_start, stuck_duration);
                end else begin
                    stage_name = "UNKNOWN";
                    detailed_analysis = "All stages have timestamps but instruction not completed";
                end
                
                // 输出详细信息
                $fwrite(final_file, "[%0d] INCOMPLETE INSTRUCTION:\n", incomplete_count);
                $fwrite(final_file, "  PC: 0x%012h\n", instr_queue[i].pc);
                $fwrite(final_file, "  Instruction: 0x%032h\n", instr_queue[i].instr);
                $fwrite(final_file, "  TInstr: 0x%013h\n", instr_queue[i].tinstr);
                $fwrite(final_file, "  ITag: 0x%08h\n", instr_queue[i].itag);
                $fwrite(final_file, "  Disassembly: \"%s\"\n", instr_queue[i].disasm_str);
                $fwrite(final_file, "  STUCK IN: %s\n", stage_name);
                $fwrite(final_file, "  Analysis: %s\n", detailed_analysis);
                
                // 输出时间线
                $fwrite(final_file, "  Timeline:\n");
                if (instr_queue[i].fetch_start > 0)
                    $fwrite(final_file, "    Fetch Start:    %.2f\n", instr_queue[i].fetch_start);
                if (instr_queue[i].fetch_end > 0)
                    $fwrite(final_file, "    Fetch End:      %.2f\n", instr_queue[i].fetch_end);
                if (instr_queue[i].decode_start > 0)
                    $fwrite(final_file, "    Decode Start:   %.2f\n", instr_queue[i].decode_start);
                if (instr_queue[i].decode_end > 0)
                    $fwrite(final_file, "    Decode End:     %.2f\n", instr_queue[i].decode_end);
                if (instr_queue[i].dispatch_start > 0)
                    $fwrite(final_file, "    Dispatch Start: %.2f\n", instr_queue[i].dispatch_start);
                if (instr_queue[i].dispatch_end > 0)
                    $fwrite(final_file, "    Dispatch End:   %.2f\n", instr_queue[i].dispatch_end);
                if (instr_queue[i].execute_start > 0)
                    $fwrite(final_file, "    Execute Start:  %.2f\n", instr_queue[i].execute_start);
                if (instr_queue[i].execute_end > 0)
                    $fwrite(final_file, "    Execute End:    %.2f\n", instr_queue[i].execute_end);
                
                $fwrite(final_file, "\n");
            end
        end
        
        // 统计信息
        $fwrite(final_file, "=== INCOMPLETE INSTRUCTIONS STATISTICS ===\n");
        $fwrite(final_file, "Total Incomplete Instructions: %0d\n", incomplete_count);
        $fwrite(final_file, "Stuck in FETCH stage:         %0d\n", stuck_in_fetch);
        $fwrite(final_file, "Stuck in DECODE stage:        %0d\n", stuck_in_decode);
        $fwrite(final_file, "Stuck in DISPATCH stage:      %0d\n", stuck_in_dispatch);
        $fwrite(final_file, "Stuck in EXECUTE stage:       %0d\n", stuck_in_execute);
        $fwrite(final_file, "\n");
        
        $fclose(final_file);
        //$display("Detailed incomplete instruction analysis saved to incomplete_instructions.log");
    end
    
    // 检查未匹配的取指请求
    fetch_req_count = fetch_req_queue.size();
    if (fetch_req_count > 0) begin
        integer fetch_file;
        fetch_file = $fopen("unmatched_fetch_requests.log", "w");
        $fwrite(fetch_file, "=== UNMATCHED FETCH REQUESTS ===\n");
        $fwrite(fetch_file, "Simulation End Time: %.2f\n", current_time);
        $fwrite(fetch_file, "Total Unmatched Requests: %0d\n\n", fetch_req_count);
        
        for (int i = 0; i < fetch_req_count; i++) begin
            // 声明块内变量
            realtime pending_time;
            pending_time = current_time - fetch_req_queue[i].fetch_time;
            $fwrite(fetch_file, "[%0d] UNMATCHED FETCH REQUEST:\n", i+1);
            $fwrite(fetch_file, "  48-bit Address: 0x%012h\n", fetch_req_queue[i].req_addr_48bit);
            $fwrite(fetch_file, "  32-bit Address: 0x%08h\n", fetch_req_queue[i].req_addr_32bit);
            $fwrite(fetch_file, "  Fetch Time:     %.2f\n", fetch_req_queue[i].fetch_time);
            $fwrite(fetch_file, "  Pending Time:   %.2f\n", pending_time);
            $fwrite(fetch_file, "\n");
        end
        
        $fclose(fetch_file);
        //$display("Unmatched fetch requests analysis saved to unmatched_fetch_requests.log");
    end
    
    // 输出简要总结
    if (incomplete_count > 0 || fetch_req_count > 0) begin
        //$display("=== INCOMPLETE ANALYSIS SUMMARY ===");
        //$display("Incomplete Instructions: %0d", incomplete_count);
        //$display("Unmatched Fetch Requests: %0d", fetch_req_count);
    end else begin
        //$display("All instructions and fetch requests completed successfully");
    end
endfunction

endmodule // itrace_monitor