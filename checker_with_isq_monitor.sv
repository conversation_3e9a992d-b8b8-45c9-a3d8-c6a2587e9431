// Checker模块示例 - 集成ISQ监控功能
// 展示如何在checker模块中include ISQ监控代码

module checker;

    // ========================================
    // 您现有的checker代码...
    // ========================================
    
    // 示例：一些checker相关的变量和逻辑
    integer error_count = 0;
    integer check_count = 0;
    
    // 示例：一些基本的检查逻辑
    initial begin
        $display("Checker module started");
        
        // 等待复位释放
        wait(`PE_TOP.pe_resetn);
        $display("Checker: Reset released, starting checks...");
        
        // 您的其他checker初始化代码...
    end
    
    // 示例：一些检查逻辑
    always @(posedge `PE_TOP.clk) begin
        if (`PE_TOP.pe_resetn) begin
            check_count = check_count + 1;
            
            // 您的检查逻辑...
            // 例如：检查某些协议或数据完整性
            
            // 示例检查（可以删除）
            if (check_count % 1000 == 0) begin
                $display("Checker: Performed %0d checks so far", check_count);
            end
        end
    end
    
    // ========================================
    // ISQ监控功能 - 只需要include一行！
    // ========================================
    `include "isq_monitor_include.sv"
    
    // ========================================
    // 您的其他checker代码继续...
    // ========================================
    
    // 仿真结束时的checker摘要
    final begin
        $display("Checker Summary:");
        $display("  Total checks performed: %0d", check_count);
        $display("  Total errors found: %0d", error_count);
        if (error_count == 0) begin
            $display("  Result: PASS - No errors detected");
        end else begin
            $display("  Result: FAIL - %0d errors detected", error_count);
        end
    end

endmodule
