# ISQ Monitor 使用说明

## 功能概述

`isq_monitor.sv` 是一个SystemVerilog模块，用于监控PE_TOP.u_tcore.tc_tfe.isq模块中的cisq_vld_cnt和misq_vld_cnt计数器的值。该模块在dispatch时统计这些计数器的值，并每1000个时钟周期dump一次数据到日志文件。

## 主要功能

1. **实时监控**: 持续监控cisq_vld_cnt和misq_vld_cnt的值
2. **定期dump**: 每1000个时钟周期输出一次统计数据
3. **利用率计算**: 自动计算队列利用率百分比
4. **满值告警**: 当队列满时输出警告信息
5. **CSV格式输出**: 便于后续数据分析

## 监控的信号

### 主要计数器
- `PE_TOP.u_tcore.tc_tfe.isq.cisq_vld_cnt` - CISQ有效计数器
- `PE_TOP.u_tcore.tc_tfe.isq.misq_vld_cnt` - MISQ有效计数器

### 辅助信号
- `PE_TOP.u_tcore.tc_tfe.isq.idu_isq_uop0_valid` - Dispatch端口0有效信号
- `PE_TOP.u_tcore.tc_tfe.isq.idu_isq_uop1_valid` - Dispatch端口1有效信号

## 输出文件格式

### isq_monitor.log
CSV格式文件，包含以下字段：

| 字段名 | 描述 | 示例 |
|--------|------|------|
| Time | 仿真时间 | 1000000 |
| CycleCount | 周期计数 | 1000 |
| CISQ_VLD_CNT | CISQ有效计数 | 15 |
| MISQ_VLD_CNT | MISQ有效计数 | 8 |
| CISQ_Utilization | CISQ利用率(%) | 46.88 |
| MISQ_Utilization | MISQ利用率(%) | 25.00 |
| Dispatch_Port0 | 端口0 dispatch状态 | 1 |
| Dispatch_Port1 | 端口1 dispatch状态 | 0 |

### 示例输出
```csv
Time,CycleCount,CISQ_VLD_CNT,MISQ_VLD_CNT,CISQ_Utilization,MISQ_Utilization,Dispatch_Port0,Dispatch_Port1
1000000,1000,15,8,46.88,25.00,1,0
2000000,2000,20,12,62.50,37.50,0,1
3000000,3000,32,16,100.00,50.00,1,1
```

## 使用方法

### 1. 集成到testbench

在您的testbench文件中添加以下内容：

```systemverilog
// 在testbench模块中实例化ISQ监控器
isq_monitor u_isq_monitor();
```

### 2. 编译和仿真

```bash
# 编译时包含isq_monitor.sv文件
vlog isq_monitor.sv your_testbench.sv

# 运行仿真
vsim your_testbench
run -all
```

### 3. 查看结果

仿真完成后，检查生成的 `isq_monitor.log` 文件：

```bash
# 查看文件内容
cat isq_monitor.log

# 或使用Excel/LibreOffice打开CSV文件进行分析
```

## 配置参数

### 可调整的参数

在 `isq_monitor.sv` 文件中，您可以修改以下参数：

```systemverilog
// 修改dump间隔（默认1000周期）
integer dump_interval = 1000;

// 修改最大计数值（默认32）
parameter MAX_COUNT = 32;

// 开启/关闭调试输出（1=开启，0=关闭）
parameter DEBUG_ENABLE = 1;
```

### 信号位宽调整

如果实际的计数器位宽不是6位，请修改信号声明：

```systemverilog
// 例如，如果计数器是8位宽
logic [7:0] cisq_vld_cnt;
logic [7:0] misq_vld_cnt;

// 同时修改最大计数值
parameter MAX_COUNT = 256;  // 2^8
```

## 调试功能

### 控制台输出

当 `DEBUG_ENABLE = 1` 时，模块会在控制台输出以下信息：

1. **初始化信息**: 模块启动时的配置信息
2. **周期性状态**: 每1000周期的ISQ状态
3. **Dispatch事件**: 每100周期的dispatch活动（如果有）
4. **满值警告**: 当队列满时的警告信息
5. **仿真结束摘要**: 总体统计信息

### 示例控制台输出

```
ISQ Monitor initialized - monitoring cisq_vld_cnt and misq_vld_cnt
Output file: isq_monitor.log
Dump interval: 1000 cycles
Max count value: 32

Time: 1000000 - ISQ Status: CISQ=15/32(46.9%), MISQ=8/32(25.0%), Cycle=1000
Time: 2000000 - ISQ Status: CISQ=20/32(62.5%), MISQ=12/32(37.5%), Cycle=2000
WARNING: CISQ is full (32/32) at time 3000000
Time: 3000000 - ISQ Status: CISQ=32/32(100.0%), MISQ=16/32(50.0%), Cycle=3000

ISQ Monitor Summary:
  Total cycles monitored: 3000
  Total dumps: 4
  Output file: isq_monitor.log
```

## 数据分析建议

### 1. 利用率分析
- 监控CISQ和MISQ的平均利用率
- 识别队列满的频率和持续时间
- 分析利用率的变化趋势

### 2. 性能瓶颈识别
- 当利用率接近100%时，可能存在性能瓶颈
- 对比两个队列的利用率差异
- 分析dispatch活动与队列状态的关系

### 3. 使用Excel/Python进行进一步分析
```python
import pandas as pd
import matplotlib.pyplot as plt

# 读取CSV文件
df = pd.read_csv('isq_monitor.log')

# 绘制利用率趋势图
plt.figure(figsize=(12, 6))
plt.plot(df['CycleCount'], df['CISQ_Utilization'], label='CISQ Utilization')
plt.plot(df['CycleCount'], df['MISQ_Utilization'], label='MISQ Utilization')
plt.xlabel('Cycle Count')
plt.ylabel('Utilization (%)')
plt.title('ISQ Utilization Over Time')
plt.legend()
plt.grid(True)
plt.show()
```

## 注意事项

1. **信号路径**: 确保信号路径 `PE_TOP.u_tcore.tc_tfe.isq.*` 与您的设计层次结构匹配
2. **位宽匹配**: 确认计数器的实际位宽并相应调整代码
3. **仿真时间**: 长时间仿真会产生大量数据，注意磁盘空间
4. **性能影响**: 频繁的文件写入可能影响仿真速度，可以适当增加dump间隔

## 故障排除

### 常见问题

1. **文件无法打开**: 检查文件权限和磁盘空间
2. **信号未连接**: 检查层次路径是否正确
3. **数据异常**: 确认计数器位宽和最大值设置

### 调试步骤

1. 启用DEBUG_ENABLE查看控制台输出
2. 检查信号连接是否正确
3. 验证时钟和复位信号
4. 确认dump间隔设置合理
