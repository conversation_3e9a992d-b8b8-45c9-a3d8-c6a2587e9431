@echo off
REM ISQ监控可视化工具启动脚本
REM 适用于Windows系统

echo ========================================
echo ISQ监控可视化工具
echo ========================================
echo.

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.6或更高版本
    pause
    exit /b 1
)

REM 检查依赖包
echo 检查Python依赖包...
python -c "import pandas, matplotlib, numpy" >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖包...
    pip install pandas matplotlib numpy
    if errorlevel 1 (
        echo 错误: 依赖包安装失败
        pause
        exit /b 1
    )
)

REM 检查ISQ监控文件
if exist "isq_monitor.log" (
    echo 找到ISQ监控文件: isq_monitor.log
    echo.
) else (
    echo 警告: 未找到默认的ISQ监控文件 isq_monitor.log
    echo.
)

REM 显示菜单
:menu
echo 请选择可视化模式:
echo 1. 快速静态图表 (推荐)
echo 2. 实时监控图表
echo 3. 完整功能版本
echo 4. 退出
echo.
set /p choice="请输入选择 (1-4): "

if "%choice%"=="1" goto quick_plot
if "%choice%"=="2" goto realtime
if "%choice%"=="3" goto full_version
if "%choice%"=="4" goto exit
echo 无效选择，请重新输入
echo.
goto menu

:quick_plot
echo 启动快速静态图表...
python quick_plot.py
if errorlevel 1 (
    echo 运行出错，请检查错误信息
    pause
)
goto menu

:realtime
echo 启动实时监控图表...
echo 提示: 如果文件不存在，监控器会等待文件出现
python realtime_monitor.py --wait
if errorlevel 1 (
    echo 运行出错，请检查错误信息
    pause
)
goto menu

:full_version
echo 启动完整功能版本...
echo 选择模式:
echo 1. 实时监控
echo 2. 静态图表
set /p mode="请选择 (1/2): "

if "%mode%"=="1" (
    python isq_visualizer.py
) else (
    python isq_visualizer.py --static
)

if errorlevel 1 (
    echo 运行出错，请检查错误信息
    pause
)
goto menu

:exit
echo 感谢使用ISQ监控可视化工具！
pause
