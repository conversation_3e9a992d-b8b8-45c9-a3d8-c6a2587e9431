# ISQ监控可视化工具使用指南

## 概述

提供了三个Python脚本用于可视化ISQ监控数据，满足不同的使用场景：

1. **`quick_plot.py`** - 快速静态图表（推荐新手使用）
2. **`realtime_monitor.py`** - 实时监控图表（仿真运行时使用）
3. **`isq_visualizer.py`** - 完整功能版本（高级用户）

## 环境要求

### 安装依赖

```bash
# 使用pip安装
pip install pandas matplotlib numpy

# 或使用conda安装
conda install pandas matplotlib numpy
```

### Python版本
- Python 3.6 或更高版本

## 使用方法

### 1. 快速静态图表 - `quick_plot.py` ⭐推荐

**最简单的使用方式，适合查看已完成仿真的结果**

```bash
# 直接运行（使用默认文件 isq_monitor.log）
python quick_plot.py

# 交互式选择显示模式
选择显示模式:
1. 分离显示 (推荐)
2. 合并显示
请选择 (1/2, 默认1): 1
```

**特点：**
- ✅ 最简单易用
- ✅ 自动统计分析
- ✅ 分离/合并两种显示模式
- ✅ 显示平均值、最大值、最小值
- ✅ 自动检测队列满的情况

### 2. 实时监控 - `realtime_monitor.py` ⭐推荐

**在仿真运行时实时查看利用率变化**

```bash
# 基本使用
python realtime_monitor.py

# 指定文件和更新间隔
python realtime_monitor.py isq_monitor.log --interval 500

# 等待文件出现（仿真还未开始时）
python realtime_monitor.py --wait
```

**特点：**
- ✅ 实时更新图表
- ✅ 自动检测新数据
- ✅ 显示当前状态信息
- ✅ 颜色告警（利用率过高时变红）
- ✅ 自动滚动显示最新数据

### 3. 完整功能版本 - `isq_visualizer.py`

**功能最全面的版本**

```bash
# 实时监控模式
python isq_visualizer.py

# 静态图表模式
python isq_visualizer.py --static

# 自定义参数
python isq_visualizer.py isq_monitor.log --interval 1000
```

## 输出示例

### 控制台输出
```
ISQ利用率快速可视化工具
========================================
正在读取文件: isq_monitor.log
读取到 1,250 条数据记录

=== ISQ利用率统计 ===
仿真时间范围: 100 - 12500
总周期数: 1,250
采样点数: 125

CISQ利用率:
  平均: 45.67%
  最大: 98.50%
  最小: 2.10%

MISQ利用率:
  平均: 38.23%
  最大: 87.30%
  最小: 0.00%

⚠️  CISQ队列满的次数: 3
```

### 图表特点

#### 分离显示模式
- 上图：CISQ利用率随时间变化
- 下图：MISQ利用率随时间变化
- 每个图都有独立的统计信息框

#### 合并显示模式
- 单个图表显示两条曲线
- 包含50%、80%、100%参考线
- 便于对比两个队列的利用率

#### 实时监控模式
- 动态更新的折线图
- 顶部状态栏显示实时信息
- 利用率过高时状态栏变色告警

## 使用场景

### 🔍 仿真后分析
```bash
# 仿真完成后，快速查看结果
python quick_plot.py
```

### 📊 仿真中监控
```bash
# 在另一个终端启动实时监控
python realtime_monitor.py --wait

# 然后启动仿真
# 监控器会自动检测并显示数据
```

### 📈 性能调优
```bash
# 使用较高的更新频率进行详细分析
python realtime_monitor.py --interval 200
```

## 高级用法

### 自定义文件路径
```bash
# 监控特定路径的文件
python quick_plot.py /path/to/your/isq_data.log
python realtime_monitor.py /path/to/your/isq_data.log
```

### 批量处理
```python
# 在Python脚本中使用
import subprocess

# 生成多个图表
files = ['pe0_isq.log', 'pe1_isq.log', 'pe2_isq.log']
for file in files:
    subprocess.run(['python', 'quick_plot.py', file])
```

### 数据导出
```python
# 读取数据进行进一步分析
import pandas as pd

df = pd.read_csv('isq_monitor.log')

# 计算自定义统计
peak_utilization = df[['CISQ_Utilization', 'MISQ_Utilization']].max()
print(f"峰值利用率: {peak_utilization}")

# 导出特定时间段的数据
filtered_data = df[(df['Time'] >= 1000) & (df['Time'] <= 5000)]
filtered_data.to_csv('filtered_isq_data.csv', index=False)
```

## 故障排除

### 常见问题

#### Q: 提示"找不到文件"
**A**: 确保ISQ监控器正在运行并生成输出文件
```bash
# 检查文件是否存在
ls -la isq_monitor.log

# 使用等待模式
python realtime_monitor.py --wait
```

#### Q: 图表显示异常
**A**: 检查数据文件格式是否正确
```bash
# 查看文件前几行
head isq_monitor.log

# 应该看到CSV头部：
# Time,CycleCount,CISQ_VLD_CNT,MISQ_VLD_CNT,CISQ_Utilization,MISQ_Utilization
```

#### Q: 实时监控不更新
**A**: 检查文件更新和权限
```bash
# 检查文件是否在更新
tail -f isq_monitor.log

# 检查文件权限
ls -la isq_monitor.log
```

#### Q: Python依赖问题
**A**: 重新安装依赖
```bash
pip install --upgrade pandas matplotlib numpy
```

## 性能优化建议

### 大数据文件处理
- 对于超过10万个数据点的文件，建议使用静态模式
- 实时监控会自动限制显示窗口大小

### 内存使用
- 实时监控器会缓存所有数据，长时间运行可能占用较多内存
- 可以定期重启监控器来释放内存

### 更新频率
- 默认500ms更新间隔适合大多数情况
- 高频仿真可以降低到200ms
- 低频仿真可以提高到1000ms或更高

## 扩展功能

可以根据需要修改脚本添加以下功能：
- 保存图表为PNG/PDF文件
- 添加更多统计指标
- 支持多文件对比
- 添加数据过滤功能
- 集成到仿真流程中
