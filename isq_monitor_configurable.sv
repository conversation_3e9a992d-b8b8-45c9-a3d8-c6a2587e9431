// ISQ Valid Count Monitor - 可配置Include版本
// 监控PE_TOP.u_tcore.tc_tfe.isq.cisq_vld_cnt和misq_vld_cnt的值
// 每N拍dump一次数据（默认10拍），不监控dispatch信号
// 
// 使用方法：
// 1. 在include之前定义配置参数（可选）
// 2. 在checker或其他模块中include此文件
//
// 示例：
// `define ISQ_DUMP_INTERVAL 20       // 自定义dump间隔（每20拍）
// `define ISQ_MAX_COUNT 64           // 自定义最大计数
// `define ISQ_DEBUG_OFF              // 关闭调试输出
// `define ISQ_OUTPUT_FILE "my_isq.log" // 自定义输出文件名
// `include "isq_monitor_configurable.sv"

// 配置参数 - 可以在include之前重新定义
`ifndef ISQ_DUMP_INTERVAL
    `define ISQ_DUMP_INTERVAL 10       // 默认每10拍dump一次
`endif

`ifndef ISQ_MAX_COUNT
    `define ISQ_MAX_COUNT 32           // 默认满值为32
`endif

`ifndef ISQ_OUTPUT_FILE
    `define ISQ_OUTPUT_FILE "isq_monitor.log"  // 默认输出文件名
`endif

`ifdef ISQ_DEBUG_OFF
    `define ISQ_DEBUG_ENABLE 0         // 关闭调试
`else
    `define ISQ_DEBUG_ENABLE 1         // 默认开启调试
`endif

// 信号路径配置 - 可以在include之前重新定义
`ifndef ISQ_TOP_PATH
    `define ISQ_TOP_PATH `PE_TOP       // 默认顶层路径
`endif

`ifndef ISQ_CLK_PATH
    `define ISQ_CLK_PATH `ISQ_TOP_PATH.clk  // 默认时钟路径
`endif

`ifndef ISQ_RESET_PATH
    `define ISQ_RESET_PATH `ISQ_TOP_PATH.pe_resetn  // 默认复位路径
`endif

`ifndef ISQ_MODULE_PATH
    `define ISQ_MODULE_PATH `ISQ_TOP_PATH.u_tcore.tc_tfe.isq  // 默认ISQ模块路径
`endif

// ISQ监控相关变量
integer isq_cycle_count = 0;
integer isq_monitor_file;

// ISQ监控初始化
initial begin
    // 打开输出文件
    isq_monitor_file = $fopen(`ISQ_OUTPUT_FILE, "w");
    if (isq_monitor_file == 0) begin
        $display("ERROR: Cannot open %s file", `ISQ_OUTPUT_FILE);
        $finish;
    end
    
    // 写入CSV头部
    $fwrite(isq_monitor_file, "Time,CycleCount,CISQ_VLD_CNT,MISQ_VLD_CNT,CISQ_Utilization,MISQ_Utilization\n");
    
    $display("ISQ Monitor (Configurable Include Version) initialized");
    $display("Output file: %s", `ISQ_OUTPUT_FILE);
    $display("Dump interval: %0d cycles", `ISQ_DUMP_INTERVAL);
    $display("Max count value: %0d", `ISQ_MAX_COUNT);
    $display("Debug enabled: %0d", `ISQ_DEBUG_ENABLE);
end

// ISQ监控主逻辑
initial begin
    // 等待复位释放
    wait(`ISQ_RESET_PATH);
    
    forever begin
        @(posedge `ISQ_CLK_PATH);
        
        // 增加周期计数
        isq_cycle_count = isq_cycle_count + 1;

        // 每N拍dump一次数据
        if (isq_cycle_count % `ISQ_DUMP_INTERVAL == 0) begin
            isq_dump_status();
        end
    end
end

// ISQ状态dump任务
task isq_dump_status();
    real cisq_utilization, misq_utilization;
    logic [5:0] cisq_cnt, misq_cnt;  // 假设6位宽，可根据实际情况调整

    // 读取当前信号值
    cisq_cnt = `ISQ_MODULE_PATH.cisq_vld_cnt;
    misq_cnt = `ISQ_MODULE_PATH.misq_vld_cnt;

    // 计算利用率（百分比）
    cisq_utilization = (real'(cisq_cnt) / real'(`ISQ_MAX_COUNT)) * 100.0;
    misq_utilization = (real'(misq_cnt) / real'(`ISQ_MAX_COUNT)) * 100.0;

    // 写入CSV格式数据
    $fwrite(isq_monitor_file, "%0t,%0d,%0d,%0d,%.2f,%.2f\n",
            $realtime, isq_cycle_count, cisq_cnt, misq_cnt,
            cisq_utilization, misq_utilization);
    
    // 控制台输出（可选）
    if (`ISQ_DEBUG_ENABLE) begin
        $display("Time: %0t - ISQ Status: CISQ=%0d/%0d(%.1f%%), MISQ=%0d/%0d(%.1f%%), Cycle=%0d",
                $realtime, cisq_cnt, `ISQ_MAX_COUNT, cisq_utilization,
                misq_cnt, `ISQ_MAX_COUNT, misq_utilization, isq_cycle_count);
    end
    
    // 检查满值情况
    if (cisq_cnt == `ISQ_MAX_COUNT) begin
        $display("WARNING: CISQ is full (%0d/%0d) at time %0t", cisq_cnt, `ISQ_MAX_COUNT, $realtime);
    end
    
    if (misq_cnt == `ISQ_MAX_COUNT) begin
        $display("WARNING: MISQ is full (%0d/%0d) at time %0t", misq_cnt, `ISQ_MAX_COUNT, $realtime);
    end
    
    // 刷新文件缓冲区
    $fflush(isq_monitor_file);
endtask

// 仿真结束时的清理
final begin
    // 最后一次dump
    if (isq_cycle_count > 0) begin
        $display("Final ISQ status dump at simulation end:");
        isq_dump_status();
    end
    
    // 关闭文件
    if (isq_monitor_file != 0) begin
        $fclose(isq_monitor_file);
    end
    
    // 输出统计摘要
    $display("ISQ Monitor Summary:");
    $display("  Total cycles monitored: %0d", isq_cycle_count);
    $display("  Total dumps: %0d", (isq_cycle_count / `ISQ_DUMP_INTERVAL) + 1);
    $display("  Output file: %s", `ISQ_OUTPUT_FILE);
end
