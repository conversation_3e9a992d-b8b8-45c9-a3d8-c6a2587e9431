# Tile Extension ISA Decoder - SystemVerilog API

这个SystemVerilog实现提供了一套完整的函数来处理Tile Extension ISA指令的解析和反编译。

## 重要说明：函数访问方式

**所有解码函数都位于 `tile_decoder_pkg` package中，可以在任何地方调用，包括initial块、always块、task和function中。**

### 正确的调用方式：

```systemverilog
// 1. 导入package
import tile_decoder_pkg::*;

// 2. 在initial块中调用
initial begin
    logic [31:0] word = 32'h0000607b;
    logic is_tile = is_tile_instruction(word);  // 直接调用
    // 或者使用完整路径
    logic is_tile2 = tile_decoder_pkg::is_tile_instruction(word);
end

// 3. 在always块中调用
always_comb begin
    output_is_tile = is_tile_instruction(input_word);
end

// 4. 在task/function中调用
task process_instruction();
    instr_collector_t collector = init_collector(word);
endtask
```

## 核心功能

### 1. 指令长度检测

```systemverilog
function automatic instr_length_e get_instruction_length(input logic [31:0] first_word);
```

**功能**: 根据第一个32位字判断完整指令的长度
**输入**: 指令的第一个32位字
**输出**: 指令长度枚举值 (INSTR_32BIT, INSTR_64BIT, INSTR_96BIT, INSTR_128BIT)

**使用示例**:
```systemverilog
logic [31:0] first_word = 32'h0000607b;
instr_length_e length = get_instruction_length(first_word);
// 返回 INSTR_64BIT
```

### 2. Tile指令识别

```systemverilog
function automatic logic is_tile_instruction(input logic [31:0] first_word);
```

**功能**: 判断指令是否为Tile Extension指令
**输入**: 指令的第一个32位字
**输出**: 1表示是tile指令，0表示不是

**使用示例**:
```systemverilog
logic [31:0] word = 32'h0000607b;
logic is_tile = is_tile_instruction(word);
// 返回 1 (true)，因为ACE_OP = 7'b1111011
```

### 3. 指令收集器

#### 初始化收集器
```systemverilog
function automatic instr_collector_t init_collector(input logic [31:0] first_word);
```

**功能**: 初始化指令收集器，开始收集多字指令
**输入**: 指令的第一个32位字
**输出**: 初始化的收集器结构

#### 添加字到收集器
```systemverilog
function automatic instr_collector_t add_word_to_collector(
    input instr_collector_t collector,
    input logic [31:0] word
);
```

**功能**: 向收集器添加下一个32位字
**输入**: 当前收集器状态和新的32位字
**输出**: 更新后的收集器状态

**使用示例**:
```systemverilog
// 处理64位指令
logic [31:0] word1 = 32'h0000607b;
logic [31:0] word2 = 32'h8000007b;

instr_collector_t collector = init_collector(word1);
// collector.is_complete = 0, collector.expected_length = INSTR_64BIT

collector = add_word_to_collector(collector, word2);
// collector.is_complete = 1, 指令收集完成
```

### 4. 指令反编译

```systemverilog
function automatic string disassemble_instruction(
    input logic [127:0] instruction_data,
    input instr_length_e length
);
```

**功能**: 将完整的指令数据反编译为汇编字符串
**输入**: 完整的指令数据(最多128位)和指令长度
**输出**: 汇编指令字符串

**使用示例**:
```systemverilog
logic [127:0] instr_data = 128'h8000007b0000607b;
string result = disassemble_instruction(instr_data, INSTR_64BIT);
// 返回 "tld.trii.linear.u32.global t0, (x0)"
```

## 数据结构

### instr_collector_t
```systemverilog
typedef struct packed {
    logic [127:0] instruction_data;  // 收集的指令数据
    logic [1:0]   collected_words;   // 已收集的字数
    instr_length_e expected_length;  // 期望的指令长度
    logic         is_complete;       // 指令是否收集完成
    logic         is_tile_instr;     // 是否为tile指令
} instr_collector_t;
```

### instr_length_e
```systemverilog
typedef enum logic [1:0] {
    INSTR_32BIT  = 2'b00,   // 32位指令
    INSTR_64BIT  = 2'b01,   // 64位指令
    INSTR_96BIT  = 2'b10,   // 96位指令
    INSTR_128BIT = 2'b11    // 128位指令
} instr_length_e;
```

## 完整使用流程

### 基本流程
```systemverilog
// 导入package
import tile_decoder_pkg::*;

module my_decoder;
    initial begin
        // 1. 接收第一个32位字
        logic [31:0] first_word = 32'h0000607b;

        // 2. 检查是否为tile指令
        if (is_tile_instruction(first_word)) begin

            // 3. 初始化收集器
            instr_collector_t collector = init_collector(first_word);

            // 4. 如果需要更多字，继续收集
            while (!collector.is_complete) begin
                logic [31:0] next_word = get_next_instruction_word();
                collector = add_word_to_collector(collector, next_word);
            end

            // 5. 反编译完整指令
            string disasm = disassemble_instruction(
                collector.instruction_data,
                collector.expected_length
            );

            $display("Tile instruction: %s", disasm);
        end
    end
endmodule
```

### 流水线集成示例
```systemverilog
// 在指令获取单元中使用
module instruction_fetch_pipeline;
    instr_collector_t collector;
    logic collector_active;
    
    always_ff @(posedge clk) begin
        if (new_instruction_word_valid) begin
            if (!collector_active) begin
                // 开始新指令
                collector <= init_collector(instruction_word);
                collector_active <= 1'b1;
            end else begin
                // 添加到现有指令
                collector <= add_word_to_collector(collector, instruction_word);
            end
            
            // 检查是否完成
            if (collector.is_complete) begin
                // 输出完整指令
                complete_instruction <= collector.instruction_data;
                instruction_ready <= 1'b1;
                collector_active <= 1'b0;
            end
        end
    end
endmodule
```

## 支持的指令类型

### 32位指令 (37个)
- CSR操作: `tcsrr.*`, `tcsrw.*`
- 同步操作: `twait.*`, `tsync.*`
- ACE扩展: `ace_*`

### 64位指令 (157个)
- 内存操作: `tld.*`, `tst.*` (linear, stride)
- 矩阵乘法: `tmma.*`

### 96位指令 (17个)
- 索引内存操作: `tld.*.index.*`, `tst.*.index.*`

### 128位指令 (4个)
- Tile复制操作: `tacp.*`

## 错误处理

- 非tile指令会被标记为32位指令
- 收集器会防止溢出（最多4个字）
- 无效的指令会返回"unknown"字符串

## 性能考虑

- 所有函数都是纯组合逻辑，可以在单个时钟周期内完成
- 指令收集器使用简单的状态机，适合流水线实现
- 字符串操作仅在仿真时使用，综合时可以用数值编码替代

## 测试和验证

使用提供的testbench验证功能：
```bash
# 运行测试
vsim -do "run -all" tile_decoder_testbench

# 启用调试输出
vsim +define+DEBUG_TILE_FETCH tile_decoder_testbench
```

测试覆盖了所有指令长度和主要指令类型，确保解码器的正确性。
