// ISQ Valid Count Monitor - Include版本
// 监控PE_TOP.u_tcore.tc_tfe.isq.cisq_vld_cnt和misq_vld_cnt的值
// 每10拍dump一次数据，不监控dispatch信号
//
// 使用方法：在checker或其他模块中include此文件
// `include "isq_monitor_include.sv"

// ISQ监控相关变量和参数
integer isq_cycle_count = 0;
integer isq_dump_interval = 10;  // 每10拍dump一次
integer isq_monitor_file;
parameter ISQ_MAX_COUNT = 32;  // 满值为32
parameter ISQ_DEBUG_ENABLE = 1;  // 调试开关

// ISQ监控初始化
initial begin
    // 打开输出文件
    isq_monitor_file = $fopen("isq_monitor.log", "w");
    if (isq_monitor_file == 0) begin
        $display("ERROR: Cannot open isq_monitor.log file");
        $finish;
    end
    
    // 写入CSV头部
    $fwrite(isq_monitor_file, "Time,CycleCount,CISQ_VLD_CNT,MISQ_VLD_CNT,CISQ_Utilization,MISQ_Utilization\n");

    $display("ISQ Monitor (Include Version) initialized");
    $display("Output file: isq_monitor.log");
    $display("Dump interval: %0d cycles", isq_dump_interval);
    $display("Max count value: %0d", ISQ_MAX_COUNT);
end

// ISQ监控主逻辑
initial begin
    // 等待复位释放
    wait(pe_resetn);
    
    forever begin
        @(posedge clk);
        
        // 增加周期计数
        isq_cycle_count = isq_cycle_count + 1;

        // 每10拍dump一次数据
        if (isq_cycle_count % isq_dump_interval == 0) begin
            isq_dump_status();
        end
    end
end

// ISQ状态dump任务
task isq_dump_status();
    real cisq_utilization, misq_utilization;
    logic [5:0] cisq_cnt, misq_cnt;  // 假设6位宽

    // 读取当前信号值
    cisq_cnt = `PE_TOP.u_tcore.tc_tfe.isq.cisq_vld_cnt;
    misq_cnt = `PE_TOP.u_tcore.tc_tfe.isq.misq_vld_cnt;

    // 计算利用率（百分比）
    cisq_utilization = (real'(cisq_cnt) / real'(ISQ_MAX_COUNT)) * 100.0;
    misq_utilization = (real'(misq_cnt) / real'(ISQ_MAX_COUNT)) * 100.0;

    // 写入CSV格式数据
    $fwrite(isq_monitor_file, "%0t,%0d,%0d,%0d,%.2f,%.2f\n",
            $realtime, isq_cycle_count, cisq_cnt, misq_cnt,
            cisq_utilization, misq_utilization);
    
    // 控制台输出（可选）
    if (ISQ_DEBUG_ENABLE) begin
        $display("Time: %0t - ISQ Status: CISQ=%0d/%0d(%.1f%%), MISQ=%0d/%0d(%.1f%%), Cycle=%0d",
                $realtime, cisq_cnt, ISQ_MAX_COUNT, cisq_utilization,
                misq_cnt, ISQ_MAX_COUNT, misq_utilization, isq_cycle_count);
    end
    
    // 检查满值情况
    if (cisq_cnt == ISQ_MAX_COUNT) begin
        $display("WARNING: CISQ is full (%0d/%0d) at time %0t", cisq_cnt, ISQ_MAX_COUNT, $realtime);
    end
    
    if (misq_cnt == ISQ_MAX_COUNT) begin
        $display("WARNING: MISQ is full (%0d/%0d) at time %0t", misq_cnt, ISQ_MAX_COUNT, $realtime);
    end
    
    // 刷新文件缓冲区
    $fflush(isq_monitor_file);
endtask

// 仿真结束时的清理
final begin
    // 最后一次dump
    if (isq_cycle_count > 0) begin
        $display("Final ISQ status dump at simulation end:");
        isq_dump_status();
    end
    
    // 关闭文件
    if (isq_monitor_file != 0) begin
        $fclose(isq_monitor_file);
    end
    
    // 输出统计摘要
    $display("ISQ Monitor Summary:");
    $display("  Total cycles monitored: %0d", isq_cycle_count);
    $display("  Total dumps: %0d", (isq_cycle_count / isq_dump_interval) + 1);
    $display("  Output file: isq_monitor.log");
end
