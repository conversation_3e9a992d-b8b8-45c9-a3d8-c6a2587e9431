# ITrace Timeline Visualizer

## 概述

ITrace Timeline Visualizer 是一个用于可视化指令执行时间线的工具，它可以从 `itrace.log` 文件读取指令执行数据，生成交互式的HTML时间线图表。

## 功能特性

### 🎯 核心功能
- **时间线可视化**: 以时间轴形式显示每条指令的执行过程
- **多阶段显示**: 清晰展示 Fetch、Decode、Dispatch、Execute 四个执行阶段
- **颜色编码**: 不同阶段使用不同颜色标识，便于区分
- **交互式界面**: 支持缩放、过滤、排序等交互操作

### 🎨 可视化特性
- **分层显示**: 每条指令占一行，各阶段在不同层级显示
- **时间比例**: 时间条长度与实际执行时间成正比
- **悬停提示**: 鼠标悬停显示详细的时间信息
- **响应式设计**: 适配不同屏幕尺寸

### 🔧 交互功能
- **缩放控制**: 0.1x - 5.0x 缩放范围
- **指令过滤**: 按PC地址或反汇编代码过滤
- **排序选项**: 按指令ID、开始时间、执行时长排序
- **统计摘要**: 显示平均执行时间等统计信息

## 文件结构

```
sv_dump/
├── itrace_timeline_visualizer.py    # 主要的Python脚本
├── test_itrace_visualizer.py        # 简化测试版本
├── test_itrace.log                  # 测试数据文件
├── itrace_timeline.html             # 生成的HTML可视化文件
└── README_ITrace_Timeline.md        # 本说明文档
```

## 使用方法

### 方法1: 使用完整版Python脚本

```bash
# 使用默认的itrace.log文件
python itrace_timeline_visualizer.py

# 指定输入文件
python itrace_timeline_visualizer.py my_itrace.log

# 指定输出文件
python itrace_timeline_visualizer.py -o my_timeline.html

# 生成后自动在浏览器中打开
python itrace_timeline_visualizer.py --open
```

### 方法2: 使用简化测试版本

```bash
# 运行测试版本（会自动创建测试数据）
python test_itrace_visualizer.py
```

### 方法3: 直接查看示例

直接在浏览器中打开 `itrace_timeline.html` 查看示例效果。

## 输入数据格式

脚本期望的 `itrace.log` 文件格式：

```csv
InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1025,1030,1035,1040,1055
3,0x000000001010,0x00000000fb0012ab,"twait",1050,1055,0,0,0
```

### 字段说明
- **InstrID**: 指令ID（整数）
- **PC**: 程序计数器地址（十六进制）
- **Instruction**: 指令编码（十六进制）
- **Disassembly**: 反汇编字符串（带引号）
- **FetchStartTime**: Fetch阶段开始时间
- **DecodeStartTime**: Decode阶段开始时间
- **DispatchStartTime**: Dispatch阶段开始时间
- **ExecuteStartTime**: Execute阶段开始时间
- **ExecuteEndTime**: Execute阶段结束时间

### 特殊情况处理
- 时间为0表示该阶段未执行
- sync/wait指令通常只有Fetch和Decode阶段
- 脚本会自动过滤无效数据

## 可视化界面说明

### 颜色编码
- 🔴 **红色 (#FF6B6B)**: Fetch 阶段
- 🔵 **青色 (#4ECDC4)**: Decode 阶段  
- 🔵 **蓝色 (#45B7D1)**: Dispatch 阶段
- 🟢 **绿色 (#96CEB4)**: Execute 阶段

### 界面布局
1. **标题栏**: 显示总体统计信息
2. **控制面板**: 缩放、过滤、排序控件
3. **时间轴**: 显示时间刻度
4. **指令列表**: 每行显示一条指令的执行时间线
5. **统计摘要**: 显示详细的执行统计

### 交互操作
- **缩放**: 拖动缩放滑块调整时间轴比例
- **过滤**: 在过滤框中输入关键词筛选指令
- **排序**: 选择不同的排序方式
- **悬停**: 鼠标悬停在时间条上查看详细信息
- **重置**: 点击"重置视图"按钮恢复默认设置

## 依赖要求

### Python依赖
```bash
pip install pandas
```

### 浏览器要求
- 现代浏览器（Chrome、Firefox、Safari、Edge）
- 支持HTML5和CSS3
- 启用JavaScript

## 故障排除

### 常见问题

#### 1. "文件不存在"错误
**解决方案**: 
- 确保 `itrace.log` 文件存在
- 使用测试版本生成示例数据

#### 2. "缺少必要的列"错误
**解决方案**: 
- 检查CSV文件格式是否正确
- 确保包含所有必需的列

#### 3. "没有有效的指令数据"
**解决方案**: 
- 检查时间数据是否有效（非零值）
- 确保至少有一条完整的指令记录

#### 4. HTML文件显示异常
**解决方案**: 
- 确保浏览器支持现代Web标准
- 检查文件编码是否为UTF-8
- 尝试在不同浏览器中打开

### 调试技巧

1. **检查数据格式**:
   ```bash
   head -5 itrace.log
   ```

2. **验证Python环境**:
   ```bash
   python -c "import pandas; print('pandas可用')"
   ```

3. **生成测试数据**:
   ```bash
   python test_itrace_visualizer.py
   ```

## 扩展功能

### 自定义颜色主题
可以修改HTML文件中的颜色定义：
```css
.stage-bar.fetch { background-color: #your-color; }
```

### 添加新的统计指标
在JavaScript中扩展统计计算逻辑。

### 导出功能
可以添加将可视化结果导出为图片的功能。

## 性能优化

### 大数据集处理
- 对于超过1000条指令的数据，建议使用采样
- 可以添加分页功能
- 考虑使用虚拟滚动

### 渲染优化
- 使用CSS transform代替重新计算位置
- 实现懒加载减少初始渲染时间

## 版本历史

- **v1.0**: 基础时间线可视化功能
- **v1.1**: 添加交互控制和统计摘要
- **v1.2**: 优化界面设计和用户体验

## 贡献指南

欢迎提交问题报告和功能建议！

## 许可证

本项目采用MIT许可证。
