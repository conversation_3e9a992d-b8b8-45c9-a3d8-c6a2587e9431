#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ITrace Timeline Visualizer
从itrace.log文件读取指令执行数据，生成HTML时间线可视化网页

Usage:
python itrace_timeline_visualizer.py [itrace.log]
"""

import pandas as pd
import json
import os
import sys
import argparse
from datetime import datetime

class ITraceTimelineVisualizer:
    def __init__(self, filename='itrace.log'):
        """
        初始化ITrace时间线可视化器
        
        Args:
            filename: itrace.log文件路径
        """
        self.filename = filename
        self.data = None
        self.max_time = 0
        self.min_time = 0
        
    def read_itrace_data(self):
        """读取itrace.log数据文件"""
        try:
            if not os.path.exists(self.filename):
                print(f"错误: 文件 {self.filename} 不存在")
                return False
                
            # 读取CSV文件
            print(f"正在读取文件: {self.filename}")
            df = pd.read_csv(self.filename)
            
            # 验证必要的列是否存在
            required_columns = ['InstrID', 'PC', 'Instruction', 'Disassembly', 
                              'FetchStartTime', 'DecodeStartTime', 'DispatchStartTime', 
                              'ExecuteStartTime', 'ExecuteEndTime']
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                print(f"错误: 缺少必要的列: {missing_columns}")
                return False
            
            # 过滤掉无效的数据行（所有时间都为0的行）
            valid_mask = (df['FetchStartTime'] > 0) | (df['DecodeStartTime'] > 0)
            df = df[valid_mask].copy()
            
            if df.empty:
                print("警告: 没有找到有效的指令数据")
                return False
                
            # 计算时间范围
            time_columns = ['FetchStartTime', 'DecodeStartTime', 'DispatchStartTime', 
                          'ExecuteStartTime', 'ExecuteEndTime']
            
            # 找到所有非零时间的最小值和最大值
            all_times = []
            for col in time_columns:
                non_zero_times = df[df[col] > 0][col]
                if not non_zero_times.empty:
                    all_times.extend(non_zero_times.tolist())
            
            if all_times:
                self.min_time = min(all_times)
                self.max_time = max(all_times)
            else:
                self.min_time = 0
                self.max_time = 1000
                
            self.data = df
            print(f"成功加载 {len(df)} 条指令记录")
            print(f"时间范围: {self.min_time} - {self.max_time}")
            return True
            
        except Exception as e:
            print(f"读取文件时出错: {e}")
            return False
    
    def process_instruction_data(self):
        """处理指令数据，计算各阶段的持续时间"""
        if self.data is None:
            return []
        
        instructions = []
        
        for _, row in self.data.iterrows():
            instr = {
                'id': int(row['InstrID']),
                'pc': row['PC'],
                'instruction': row['Instruction'],
                'disassembly': row['Disassembly'],
                'stages': []
            }
            
            # 处理各个执行阶段
            stages_info = [
                ('fetch', row['FetchStartTime'], row['DecodeStartTime'], '#FF6B6B'),
                ('decode', row['DecodeStartTime'], row['DispatchStartTime'], '#4ECDC4'),
                ('dispatch', row['DispatchStartTime'], row['ExecuteStartTime'], '#45B7D1'),
                ('execute', row['ExecuteStartTime'], row['ExecuteEndTime'], '#96CEB4')
            ]
            
            for stage_name, start_time, end_time, color in stages_info:
                if start_time > 0:
                    # 如果end_time为0或小于start_time，使用start_time作为end_time
                    if end_time <= 0 or end_time < start_time:
                        if stage_name == 'fetch':
                            # fetch阶段如果没有结束时间，假设持续1个时间单位
                            end_time = start_time + 1
                        else:
                            # 其他阶段如果没有结束时间，跳过
                            continue
                    
                    stage = {
                        'name': stage_name,
                        'start': int(start_time),
                        'end': int(end_time),
                        'duration': int(end_time - start_time),
                        'color': color
                    }
                    instr['stages'].append(stage)
            
            # 计算总执行时间
            if instr['stages']:
                instr['total_start'] = min(stage['start'] for stage in instr['stages'])
                instr['total_end'] = max(stage['end'] for stage in instr['stages'])
                instr['total_duration'] = instr['total_end'] - instr['total_start']
            else:
                instr['total_start'] = 0
                instr['total_end'] = 0
                instr['total_duration'] = 0
            
            instructions.append(instr)
        
        # 按照指令ID排序
        instructions.sort(key=lambda x: x['id'])
        return instructions

    def generate_html_timeline(self, instructions, output_filename='itrace_timeline.html'):
        """生成HTML时间线可视化"""

        # 计算时间轴的缩放比例
        time_range = self.max_time - self.min_time
        if time_range == 0:
            time_range = 1000

        # HTML模板
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ITrace 指令执行时间线</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}

        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}

        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            text-align: center;
        }}

        .stats {{
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
            font-size: 1.1em;
        }}

        .timeline-container {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }}

        .timeline-header {{
            display: flex;
            align-items: center;
            margin-bottom: 20px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 8px;
        }}

        .legend {{
            display: flex;
            gap: 20px;
            margin-left: auto;
        }}

        .legend-item {{
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }}

        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }}

        .time-axis {{
            position: relative;
            height: 40px;
            margin-bottom: 20px;
            border-bottom: 2px solid #ddd;
        }}

        .time-tick {{
            position: absolute;
            bottom: 0;
            width: 1px;
            height: 10px;
            background: #666;
        }}

        .time-label {{
            position: absolute;
            bottom: -25px;
            font-size: 12px;
            color: #666;
            transform: translateX(-50%);
        }}

        .instruction-row {{
            display: flex;
            align-items: center;
            margin-bottom: 15px;
            padding: 10px;
            background: #fafafa;
            border-radius: 8px;
            border-left: 4px solid #007bff;
            transition: all 0.3s ease;
        }}

        .instruction-row:hover {{
            background: #e3f2fd;
            transform: translateX(5px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }}

        .instruction-info {{
            width: 300px;
            flex-shrink: 0;
            padding-right: 20px;
        }}

        .instruction-id {{
            font-weight: bold;
            color: #007bff;
            font-size: 1.1em;
        }}

        .instruction-pc {{
            color: #666;
            font-family: monospace;
            font-size: 0.9em;
        }}

        .instruction-disasm {{
            color: #333;
            font-family: monospace;
            font-size: 0.9em;
            margin-top: 5px;
            word-break: break-all;
        }}

        .timeline-bars {{
            flex: 1;
            position: relative;
            height: 60px;
            min-width: 800px;
        }}

        .stage-bar {{
            position: absolute;
            height: 12px;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 10px;
            font-weight: bold;
            color: white;
            text-shadow: 1px 1px 1px rgba(0,0,0,0.5);
            cursor: pointer;
            transition: all 0.2s ease;
        }}

        .stage-bar:hover {{
            transform: scaleY(1.2);
            z-index: 10;
        }}

        .stage-bar.fetch {{ top: 5px; }}
        .stage-bar.decode {{ top: 20px; }}
        .stage-bar.dispatch {{ top: 35px; }}
        .stage-bar.execute {{ top: 50px; }}

        .tooltip {{
            position: absolute;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
            pointer-events: none;
            z-index: 1000;
            opacity: 0;
            transition: opacity 0.2s;
        }}

        .controls {{
            margin-bottom: 20px;
            display: flex;
            gap: 15px;
            align-items: center;
            flex-wrap: wrap;
        }}

        .control-group {{
            display: flex;
            align-items: center;
            gap: 8px;
        }}

        .control-group label {{
            font-weight: 500;
            color: #555;
        }}

        .control-group input, .control-group select {{
            padding: 5px 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
        }}

        .btn {{
            padding: 8px 16px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background 0.2s;
        }}

        .btn:hover {{
            background: #0056b3;
        }}

        .summary {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            margin-top: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }}

        .summary h3 {{
            margin-top: 0;
            color: #333;
            border-bottom: 2px solid #007bff;
            padding-bottom: 10px;
        }}

        .summary-grid {{
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }}

        .summary-item {{
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            text-align: center;
        }}

        .summary-value {{
            font-size: 1.5em;
            font-weight: bold;
            color: #007bff;
        }}

        .summary-label {{
            color: #666;
            margin-top: 5px;
        }}
    </style>
</head>
<body>
    <div class="header">
        <h1>ITrace 指令执行时间线可视化</h1>
        <div class="stats">
            <div>总指令数: <strong>{total_instructions}</strong></div>
            <div>时间范围: <strong>{min_time} - {max_time}</strong></div>
            <div>总执行时间: <strong>{total_time}</strong></div>
            <div>生成时间: <strong>{generation_time}</strong></div>
        </div>
    </div>

    <div class="timeline-container">
        <div class="timeline-header">
            <h3 style="margin: 0;">指令执行时间线</h3>
            <div class="legend">
                <div class="legend-item">
                    <div class="legend-color" style="background: #FF6B6B;"></div>
                    <span>Fetch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #4ECDC4;"></div>
                    <span>Decode</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #45B7D1;"></div>
                    <span>Dispatch</span>
                </div>
                <div class="legend-item">
                    <div class="legend-color" style="background: #96CEB4;"></div>
                    <span>Execute</span>
                </div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <label>缩放:</label>
                <input type="range" id="zoomSlider" min="0.1" max="5" step="0.1" value="1">
                <span id="zoomValue">1.0x</span>
            </div>
            <div class="control-group">
                <label>过滤指令:</label>
                <input type="text" id="filterInput" placeholder="输入PC地址或反汇编代码...">
            </div>
            <div class="control-group">
                <label>排序:</label>
                <select id="sortSelect">
                    <option value="id">指令ID</option>
                    <option value="start_time">开始时间</option>
                    <option value="duration">执行时长</option>
                </select>
            </div>
            <button class="btn" onclick="resetView()">重置视图</button>
        </div>

        <div class="time-axis" id="timeAxis"></div>

        <div id="instructionList">
            {instruction_rows}
        </div>
    </div>

    <div class="tooltip" id="tooltip"></div>

    <div class="summary">
        <h3>执行统计摘要</h3>
        <div class="summary-grid">
            {summary_stats}
        </div>
    </div>

    <script>
        const instructions = {instructions_json};
        const minTime = {min_time};
        const maxTime = {max_time};
        const timeRange = maxTime - minTime;

        let currentZoom = 1.0;
        let currentFilter = '';
        let currentSort = 'id';

        // 初始化时间轴
        function initTimeAxis() {{{{
            const timeAxis = document.getElementById('timeAxis');
            const tickCount = 10;

            for (let i = 0; i <= tickCount; i++) {{{{
                const time = minTime + (timeRange * i / tickCount);
                const position = (i / tickCount) * 100;

                const tick = document.createElement('div');
                tick.className = 'time-tick';
                tick.style.left = position + '%';
                timeAxis.appendChild(tick);

                const label = document.createElement('div');
                label.className = 'time-label';
                label.style.left = position + '%';
                label.textContent = Math.round(time);
                timeAxis.appendChild(label);
            }}}}
        }}}}

        // 计算时间条的位置和宽度
        function calculateBarPosition(start, end) {{{{
            const startPercent = ((start - minTime) / timeRange) * 100;
            const widthPercent = ((end - start) / timeRange) * 100;
            return {{{{ left: startPercent, width: Math.max(widthPercent, 0.5) }}}};
        }}}}

        // 显示工具提示
        function showTooltip(event, stage, instruction) {{{{
            const tooltip = document.getElementById('tooltip');
            tooltip.innerHTML = `
                <strong>${{{{stage.name.toUpperCase()}}}}</strong><br>
                指令: ${{{{instruction.disassembly}}}}<br>
                开始: ${{{{stage.start}}}}<br>
                结束: ${{{{stage.end}}}}<br>
                持续: ${{{{stage.duration}}}}
            `;
            tooltip.style.left = event.pageX + 10 + 'px';
            tooltip.style.top = event.pageY - 10 + 'px';
            tooltip.style.opacity = '1';
        }}}}

        // 隐藏工具提示
        function hideTooltip() {{{{
            document.getElementById('tooltip').style.opacity = '0';
        }}}}

        // 渲染指令列表
        function renderInstructions(filteredInstructions = null) {{{{
            const instructionList = document.getElementById('instructionList');
            const instructionsToRender = filteredInstructions || instructions;

            instructionList.innerHTML = '';

            instructionsToRender.forEach(instruction => {{{{
                const row = document.createElement('div');
                row.className = 'instruction-row';

                const info = document.createElement('div');
                info.className = 'instruction-info';
                info.innerHTML = `
                    <div class="instruction-id">指令 #${{{{instruction.id}}}}</div>
                    <div class="instruction-pc">${{{{instruction.pc}}}}</div>
                    <div class="instruction-disasm">${{{{instruction.disassembly}}}}</div>
                `;

                const timeline = document.createElement('div');
                timeline.className = 'timeline-bars';

                instruction.stages.forEach(stage => {{{{
                    const bar = document.createElement('div');
                    bar.className = `stage-bar ${{{{stage.name}}}}`;
                    bar.style.backgroundColor = stage.color;

                    const position = calculateBarPosition(stage.start, stage.end);
                    bar.style.left = position.left + '%';
                    bar.style.width = position.width + '%';

                    if (position.width > 2) {{{{
                        bar.textContent = stage.name;
                    }}}}

                    bar.addEventListener('mouseenter', (e) => showTooltip(e, stage, instruction));
                    bar.addEventListener('mouseleave', hideTooltip);

                    timeline.appendChild(bar);
                }}}});

                row.appendChild(info);
                row.appendChild(timeline);
                instructionList.appendChild(row);
            }}}});
        }}}}

        // 过滤指令
        function filterInstructions() {{{{
            const filter = document.getElementById('filterInput').value.toLowerCase();
            if (!filter) {{{{
                renderInstructions();
                return;
            }}}}

            const filtered = instructions.filter(instr =>
                instr.pc.toLowerCase().includes(filter) ||
                instr.disassembly.toLowerCase().includes(filter)
            );

            renderInstructions(filtered);
        }}}}

        // 排序指令
        function sortInstructions() {{{{
            const sortBy = document.getElementById('sortSelect').value;
            const sorted = [...instructions];

            sorted.sort((a, b) => {{{{
                switch(sortBy) {{{{
                    case 'start_time':
                        return a.total_start - b.total_start;
                    case 'duration':
                        return b.total_duration - a.total_duration;
                    default:
                        return a.id - b.id;
                }}}}
            }}}});

            renderInstructions(sorted);
        }}}}

        // 缩放功能
        function updateZoom() {{{{
            const zoom = document.getElementById('zoomSlider').value;
            document.getElementById('zoomValue').textContent = parseFloat(zoom).toFixed(1) + 'x';

            const timelineBars = document.querySelectorAll('.timeline-bars');
            timelineBars.forEach(bar => {{{{
                bar.style.minWidth = (800 * zoom) + 'px';
            }}}});
        }}}}

        // 重置视图
        function resetView() {{{{
            document.getElementById('zoomSlider').value = 1;
            document.getElementById('filterInput').value = '';
            document.getElementById('sortSelect').value = 'id';
            updateZoom();
            renderInstructions();
        }}}}

        // 事件监听器
        document.getElementById('zoomSlider').addEventListener('input', updateZoom);
        document.getElementById('filterInput').addEventListener('input', filterInstructions);
        document.getElementById('sortSelect').addEventListener('change', sortInstructions);

        // 初始化
        initTimeAxis();
        renderInstructions();
    </script>
</body>
</html>
        """

        return html_template

    def generate_instruction_rows(self, instructions):
        """生成指令行的HTML"""
        rows = []

        for instr in instructions:
            if not instr['stages']:
                continue

            # 生成时间条HTML
            stage_bars = []
            for stage in instr['stages']:
                start_percent = ((stage['start'] - self.min_time) / (self.max_time - self.min_time)) * 100
                width_percent = ((stage['end'] - stage['start']) / (self.max_time - self.min_time)) * 100
                width_percent = max(width_percent, 0.5)  # 最小宽度

                stage_bars.append(f"""
                    <div class="stage-bar {stage['name']}"
                         style="left: {start_percent:.2f}%; width: {width_percent:.2f}%; background-color: {stage['color']};"
                         onmouseenter="showTooltip(event, {json.dumps(stage)}, {json.dumps(instr)})"
                         onmouseleave="hideTooltip()">
                        {stage['name'] if width_percent > 2 else ''}
                    </div>
                """)

            row_html = f"""
                <div class="instruction-row">
                    <div class="instruction-info">
                        <div class="instruction-id">指令 #{instr['id']}</div>
                        <div class="instruction-pc">{instr['pc']}</div>
                        <div class="instruction-disasm">{instr['disassembly']}</div>
                    </div>
                    <div class="timeline-bars">
                        {''.join(stage_bars)}
                    </div>
                </div>
            """
            rows.append(row_html)

        return '\n'.join(rows)

    def generate_summary_stats(self, instructions):
        """生成统计摘要"""
        if not instructions:
            return ""

        # 计算各种统计数据
        total_instructions = len(instructions)

        # 各阶段平均持续时间
        stage_durations = {'fetch': [], 'decode': [], 'dispatch': [], 'execute': []}
        total_durations = []

        for instr in instructions:
            if instr['total_duration'] > 0:
                total_durations.append(instr['total_duration'])

            for stage in instr['stages']:
                if stage['name'] in stage_durations:
                    stage_durations[stage['name']].append(stage['duration'])

        # 计算平均值
        avg_total = sum(total_durations) / len(total_durations) if total_durations else 0
        avg_fetch = sum(stage_durations['fetch']) / len(stage_durations['fetch']) if stage_durations['fetch'] else 0
        avg_decode = sum(stage_durations['decode']) / len(stage_durations['decode']) if stage_durations['decode'] else 0
        avg_dispatch = sum(stage_durations['dispatch']) / len(stage_durations['dispatch']) if stage_durations['dispatch'] else 0
        avg_execute = sum(stage_durations['execute']) / len(stage_durations['execute']) if stage_durations['execute'] else 0

        # 最长和最短执行时间
        max_duration = max(total_durations) if total_durations else 0
        min_duration = min(total_durations) if total_durations else 0

        # 生成HTML
        stats_html = f"""
            <div class="summary-item">
                <div class="summary-value">{total_instructions}</div>
                <div class="summary-label">总指令数</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{avg_total:.1f}</div>
                <div class="summary-label">平均执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{max_duration}</div>
                <div class="summary-label">最长执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{min_duration}</div>
                <div class="summary-label">最短执行时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{avg_fetch:.1f}</div>
                <div class="summary-label">平均Fetch时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{avg_decode:.1f}</div>
                <div class="summary-label">平均Decode时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{avg_dispatch:.1f}</div>
                <div class="summary-label">平均Dispatch时间</div>
            </div>
            <div class="summary-item">
                <div class="summary-value">{avg_execute:.1f}</div>
                <div class="summary-label">平均Execute时间</div>
            </div>
        """

        return stats_html

    def create_visualization(self, output_filename='itrace_timeline.html'):
        """创建完整的可视化HTML文件"""
        if not self.read_itrace_data():
            return False

        instructions = self.process_instruction_data()
        if not instructions:
            print("没有有效的指令数据可以可视化")
            return False

        print(f"正在生成HTML可视化文件: {output_filename}")

        # 生成各部分内容
        instruction_rows = self.generate_instruction_rows(instructions)
        summary_stats = self.generate_summary_stats(instructions)

        # 填充HTML模板
        html_content = self.generate_html_timeline(instructions, output_filename)
        html_content = html_content.format(
            total_instructions=len(instructions),
            min_time=int(self.min_time),
            max_time=int(self.max_time),
            total_time=int(self.max_time - self.min_time),
            generation_time=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            instruction_rows=instruction_rows,
            instructions_json=json.dumps(instructions, ensure_ascii=False),
            summary_stats=summary_stats
        )

        # 写入文件
        try:
            with open(output_filename, 'w', encoding='utf-8') as f:
                f.write(html_content)

            print(f"HTML可视化文件已生成: {output_filename}")
            print(f"包含 {len(instructions)} 条指令的时间线")
            print(f"时间范围: {self.min_time} - {self.max_time}")
            return True

        except Exception as e:
            print(f"写入HTML文件时出错: {e}")
            return False

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='ITrace Timeline Visualizer - 生成指令执行时间线HTML可视化')
    parser.add_argument('input_file', nargs='?', default='itrace.log',
                       help='输入的itrace.log文件路径 (默认: itrace.log)')
    parser.add_argument('-o', '--output', default='itrace_timeline.html',
                       help='输出的HTML文件路径 (默认: itrace_timeline.html)')
    parser.add_argument('--open', action='store_true',
                       help='生成后自动在浏览器中打开HTML文件')

    args = parser.parse_args()

    print("=" * 60)
    print("ITrace Timeline Visualizer")
    print("=" * 60)
    print(f"输入文件: {args.input_file}")
    print(f"输出文件: {args.output}")
    print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("-" * 60)

    # 检查输入文件是否存在
    if not os.path.exists(args.input_file):
        print(f"错误: 输入文件 '{args.input_file}' 不存在")
        print("请确保itrace.log文件已经生成")
        sys.exit(1)

    # 创建可视化器
    visualizer = ITraceTimelineVisualizer(args.input_file)

    # 生成可视化
    success = visualizer.create_visualization(args.output)

    if success:
        print("-" * 60)
        print("✅ 可视化生成成功!")
        print(f"📁 输出文件: {os.path.abspath(args.output)}")

        # 如果指定了--open参数，尝试在浏览器中打开
        if args.open:
            try:
                import webbrowser
                webbrowser.open(f'file://{os.path.abspath(args.output)}')
                print("🌐 已在浏览器中打开HTML文件")
            except Exception as e:
                print(f"⚠️  无法自动打开浏览器: {e}")
                print("请手动在浏览器中打开HTML文件")
        else:
            print("💡 使用 --open 参数可以自动在浏览器中打开文件")
    else:
        print("❌ 可视化生成失败")
        sys.exit(1)

def create_test_data():
    """创建测试数据用于验证功能"""
    test_data = """InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1025,1030,1035,1040,1055
3,0x000000001010,0x00000000fb0012ab,"twait",1050,1055,0,0,0
4,0x000000001014,0x00000000fb801000,"ace_bsync x0",1060,1065,0,0,0
5,0x000000001018,0x00000000fb345678,"tcsrw.i 0x5",1070,1075,1080,1085,1090
6,0x000000001020,0x00000000fb456789,"tld.linear.u16 t6, (x11)",1095,1100,1105,1110,1125
7,0x000000001028,0x00000000fb567890,"tmma.tnt t4, t2, t3",1130,1135,1140,1145,1165
8,0x000000001030,0x00000000fb678901,"tst.linear.u32 (x12), t7",1170,1175,1180,1185,1195
"""

    with open('test_itrace.log', 'w', encoding='utf-8') as f:
        f.write(test_data)

    print("测试数据已生成: test_itrace.log")
    return 'test_itrace.log'

if __name__ == "__main__":
    # 如果没有提供参数且itrace.log不存在，创建测试数据
    if len(sys.argv) == 1 and not os.path.exists('itrace.log'):
        print("未找到itrace.log文件，正在创建测试数据...")
        test_file = create_test_data()
        print(f"使用测试数据: {test_file}")
        sys.argv.append(test_file)

    main()
