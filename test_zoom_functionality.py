#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试缩放功能的脚本
"""

import os
import sys
import time
import webbrowser
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.webdriver.chrome.options import Options

def test_zoom_functionality():
    """测试缩放功能是否正常工作"""
    
    # 检查HTML文件是否存在
    html_file = 'itrace_timeline.html'
    if not os.path.exists(html_file):
        print("错误: HTML文件不存在，请先运行 itrace_timeline_visualizer.py")
        return False
    
    print("开始测试缩放功能...")
    
    # 配置Chrome选项
    chrome_options = Options()
    chrome_options.add_argument('--headless')  # 无头模式
    chrome_options.add_argument('--no-sandbox')
    chrome_options.add_argument('--disable-dev-shm-usage')
    
    try:
        # 启动浏览器
        driver = webdriver.Chrome(options=chrome_options)
        
        # 打开HTML文件
        file_path = f"file://{os.path.abspath(html_file)}"
        driver.get(file_path)
        
        # 等待页面加载
        wait = WebDriverWait(driver, 10)
        zoom_slider = wait.until(EC.presence_of_element_located((By.ID, "zoomSlider")))
        
        print("✅ 页面加载成功")
        
        # 测试缩放滑块是否存在
        zoom_value_display = driver.find_element(By.ID, "zoomValue")
        initial_value = zoom_value_display.text
        print(f"初始缩放值: {initial_value}")
        
        # 测试设置不同的缩放值
        test_values = [0.5, 1.5, 2.0, 3.0]
        
        for zoom in test_values:
            # 设置缩放值
            driver.execute_script(f"document.getElementById('zoomSlider').value = {zoom};")
            driver.execute_script("updateZoom();")
            
            # 检查显示值是否更新
            time.sleep(0.5)  # 等待更新
            current_value = zoom_value_display.text
            expected_value = f"{zoom:.1f}x"
            
            if current_value == expected_value:
                print(f"✅ 缩放值 {zoom} 测试通过: {current_value}")
            else:
                print(f"❌ 缩放值 {zoom} 测试失败: 期望 {expected_value}, 实际 {current_value}")
                return False
            
            # 检查时间线容器宽度是否改变
            timeline_bars = driver.find_elements(By.CLASS_NAME, "timeline-bars")
            if timeline_bars:
                min_width = timeline_bars[0].value_of_css_property("min-width")
                expected_width = f"{800 * zoom}px"
                if min_width == expected_width:
                    print(f"✅ 时间线宽度正确: {min_width}")
                else:
                    print(f"⚠️  时间线宽度: 期望 {expected_width}, 实际 {min_width}")
        
        # 测试重置功能
        driver.execute_script("resetView();")
        time.sleep(0.5)
        
        reset_value = zoom_value_display.text
        if reset_value == "1.0x":
            print("✅ 重置功能正常")
        else:
            print(f"❌ 重置功能失败: {reset_value}")
            return False
        
        print("🎉 所有缩放功能测试通过!")
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出错: {e}")
        return False
    
    finally:
        if 'driver' in locals():
            driver.quit()

def manual_test_instructions():
    """提供手动测试说明"""
    print("\n" + "="*60)
    print("手动测试说明:")
    print("="*60)
    print("1. 在浏览器中打开 itrace_timeline.html")
    print("2. 找到页面上的缩放滑块（标签为'缩放:'）")
    print("3. 左右拖动滑块，观察以下变化：")
    print("   - 缩放值显示（如 1.0x, 2.0x 等）应该实时更新")
    print("   - 时间线条的宽度应该随缩放变化")
    print("   - 时间轴刻度的间距应该随缩放变化")
    print("   - 指令执行阶段的条形图应该随缩放拉伸或压缩")
    print("4. 测试不同缩放值（0.1x 到 5.0x）")
    print("5. 点击'重置视图'按钮，确认缩放回到 1.0x")
    print("6. 在缩放状态下，时间线应该支持水平滚动")
    print("="*60)

if __name__ == "__main__":
    print("ITrace Timeline 缩放功能测试")
    print("="*40)
    
    # 尝试自动测试（需要selenium）
    try:
        import selenium
        if test_zoom_functionality():
            print("\n✅ 自动测试完成，缩放功能正常!")
        else:
            print("\n❌ 自动测试发现问题")
    except ImportError:
        print("未安装selenium，跳过自动测试")
        print("可以运行: pip install selenium")
    
    # 提供手动测试说明
    manual_test_instructions()
    
    # 打开HTML文件供手动测试
    html_file = 'itrace_timeline.html'
    if os.path.exists(html_file):
        try:
            webbrowser.open(f'file://{os.path.abspath(html_file)}')
            print(f"\n🌐 已在浏览器中打开 {html_file} 供手动测试")
        except Exception as e:
            print(f"⚠️  无法自动打开浏览器: {e}")
            print(f"请手动在浏览器中打开: {os.path.abspath(html_file)}")
    else:
        print(f"\n❌ 找不到 {html_file}，请先运行 itrace_timeline_visualizer.py")
