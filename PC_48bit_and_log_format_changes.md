# PC 48位扩展和Log格式修改说明

## 修改概述

根据要求，对 `itrace.sv` 文件进行了以下主要修改：

1. **PC字段扩展**：将PC从32位扩展到48位
2. **取指请求比较逻辑**：取指请求时使用48位比较，其他时候只比较低32位
3. **打印格式更新**：所有debug log中的PC都打印48位
4. **最终log格式修改**：不打印duration，只打印各阶段的开始时间和execute end时间
5. **关键修正**：collector_start_pc现在使用取指请求时的48位PC，而不是取指返回时的32位PC

## 详细修改内容

### 1. 数据结构修改

#### 指令跟踪结构 (instr_perf_t)
```systemverilog
// 修改前
logic [31:0] pc;

// 修改后  
logic [47:0] pc;      // 扩展到48位PC
```

#### 取指请求结构 (fetch_req_t)
```systemverilog
// 修改前
typedef struct {
    logic [31:0] req_addr;      // 32bit取指地址
    realtime fetch_time;        // 取指开始时间
} fetch_req_32bit_t;

// 修改后
typedef struct {
    logic [47:0] req_addr_48bit; // 48bit完整取指地址（用于取指请求时的比较）
    logic [31:0] req_addr_32bit; // 32bit取指地址（用于其他时候的比较）
    realtime fetch_time;         // 取指开始时间
} fetch_req_t;
```

### 2. 信号连接修改

```systemverilog
// 修改前
logic [31:0] req_addr;
logic [31:0] ifu_i0_pc, ifu_i1_pc;

// 修改后
logic [47:0] req_addr;  // 扩展到48位
logic [47:0] ifu_i0_pc, ifu_i1_pc;  // 扩展到48位

// 信号连接（假设硬件信号是32位，通过零扩展到48位）
assign req_addr = {16'h0, `PE_TOP.u_ax45mpv_cluster_wrapper.u_complex0_wrapper.u_complex.u_core0.ax45mpv_core.kv_core.kv_ifu.req_addr};
assign ifu_i0_pc = {16'h0, `PE_TOP.u_ax45mpv_cluster_wrapper.u_complex0_wrapper.u_complex.u_core0.ax45mpv_core.kv_core.kv_ifu.ifu_i0_pc};
assign ifu_i1_pc = {16'h0, `PE_TOP.u_ax45mpv_cluster_wrapper.u_complex0_wrapper.u_complex.u_core0.ax45mpv_core.kv_core.kv_ifu.ifu_i1_pc};
```

### 3. 函数修改

#### 取指请求添加函数
```systemverilog
// 修改前
function automatic void add_32bit_fetch_request(logic [31:0] req_addr, realtime fetch_time);

// 修改后
function automatic void add_fetch_request(logic [47:0] req_addr_48bit, realtime fetch_time);
```
- 取指请求时使用48位地址进行比较
- 同时存储48位和32位地址

#### 取指请求查找函数
```systemverilog
// 修改前
function automatic realtime find_and_remove_fetch_time(logic [31:0] pc_addr);

// 修改后
function automatic realtime find_and_remove_fetch_time(logic [47:0] pc_addr_48bit);
```
- 其他时候只比较低32位地址
- 打印log时显示完整的48位地址

#### 新增：48位PC获取函数
```systemverilog
// 新增函数：根据32bit PC查找并返回48bit PC和取指时间
function automatic void find_and_remove_fetch_info(logic [31:0] pc_addr_32bit, output logic [47:0] pc_addr_48bit, output realtime fetch_time);
```
- 用于从取指返回的32位PC获取取指请求时的48位PC
- 确保collector_start_pc使用正确的48位PC

### 4. Log格式修改

#### CSV头部
```systemverilog
// 修改前
"InstrID,PC,Instruction,TileInstr,Disassembly,ITag,FetchStartTime,FetchTime,DecodeTime,DispatchTime,ExecuteTime,TotalTime\n"

// 修改后
"InstrID,PC,Instruction,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime\n"
```

#### 最终输出格式
```systemverilog
// 修改前
$fwrite(perf_file, "%0d,0x%08h,0x%032h,0x%013h,\"%s\",0x%08h,%.2f,%.2f,%.2f,%.2f,%.2f,%.2f\n",
       instr_id, instr_queue[i].pc, instr_queue[i].instr, instr_queue[i].tinstr,
       instr_queue[i].disasm_str, instr_queue[i].itag,
       instr_queue[i].fetch_start, fetch_time, decode_time, dispatch_time, execute_time, total_time);

// 修改后
$fwrite(perf_file, "%0d,0x%012h,0x%032h,%0d,%0d,%0d,%0d,%0d\n",
       instr_id, instr_queue[i].pc, instr_queue[i].instr,
       int'(instr_queue[i].fetch_start), int'(instr_queue[i].decode_start), int'(instr_queue[i].dispatch_start),
       int'(instr_queue[i].execute_start), int'(instr_queue[i].execute_end));
```

### 5. Debug打印格式修改

所有debug打印中的PC格式从 `0x%08h` 改为 `0x%012h`，以显示完整的48位地址。

## 比较逻辑说明

### 取指请求时（48位比较）
- 使用完整的48位地址进行比较
- 确保取指请求的精确匹配

### 其他时候（32位比较）  
- 只比较低32位地址
- 兼容现有的32位PC处理逻辑
- 减少比较开销

## 输出文件格式

### itrace.log
新的简化CSV格式包含以下字段：
1. InstrID - 指令ID
2. PC - 48位PC地址（0x%012h格式）
3. Instruction - 128位指令数据
4. FetchStartTime - 取指开始时间（整数格式）
5. DecodeStartTime - 解码开始时间（整数格式）
6. DispatchStartTime - 分发开始时间（整数格式）
7. ExecuteStartTime - 执行开始时间（整数格式）
8. ExecuteEndTime - 执行结束时间（整数格式）

**移除的字段**：
- TileInstr - 51位tile指令（不再输出）
- Disassembly - 反编译字符串（不再输出）
- ITag - 指令标签（不再输出）

**时间格式**：
- 所有时间字段都以整数格式输出，不显示小数点
- 使用 `int'()` 转换将realtime转换为整数

### itrace_debug.log
所有debug信息中的PC地址都以48位格式显示。

## 关键修正：collector_start_pc的正确获取

### 问题描述
原来的实现中，`collector_start_pc` 直接使用取指返回时的PC（如 `ifu_i0_pc`），但这个PC在硬件上只有32位。而取指请求时的PC是48位的。

### 解决方案
1. **新增函数**：`find_and_remove_fetch_info()` 用于根据32位PC查找对应的48位PC
2. **修改逻辑**：在开始收集tile指令时，使用新函数获取取指请求时的48位PC
3. **统一管理**：添加 `collector_fetch_time` 变量统一管理收集器的取指时间

### 修改示例
```systemverilog
// 修改前
collector_start_pc = ifu_i0_pc;  // 使用32位PC（错误）

// 修改后
find_and_remove_fetch_info(ifu_i0_pc[31:0], collector_start_pc, collector_fetch_time);  // 获取48位PC（正确）
```

### 影响范围
- 所有tile指令收集的开始阶段
- 确保最终输出的PC是取指请求时的完整48位地址
- 保证PC地址的一致性和准确性

## 兼容性说明

- 如果硬件信号实际是48位，可以直接连接，无需零扩展
- 如果硬件信号是32位，通过零扩展到48位处理
- 保持了与现有tile指令解码器的兼容性
- 新的PC获取逻辑确保了取指请求时PC的准确性
