// 测试 is_sync_or_wait_instruction 函数的简单测试模块

import tile_decoder_pkg::*;

module test_sync_wait_function;

    initial begin
        logic [31:0] test_instructions[0:7];
        logic result;
        string instr_type;
        
        $display("=== Testing is_sync_or_wait_instruction Function ===");
        
        // 测试指令集合
        test_instructions[0] = 32'h0000407b; // 普通tile指令 (tuop_100 - CSR)
        test_instructions[1] = 32'h0000607b; // 普通tile指令 (tuop_000 - memory)
        test_instructions[2] = 32'h0000a07b; // tuop_101 sync指令 (twait)
        test_instructions[3] = 32'h8000a07b; // tuop_101 sync指令 (tsync)
        test_instructions[4] = 32'h8000e07b; // tuop_111 ACE指令 (ace_bsync)
        test_instructions[5] = 32'h9400e07b; // tuop_111 ACE指令 (ace_nbsync)
        test_instructions[6] = 32'h12345678; // 非tile指令
        test_instructions[7] = 32'h0000c07b; // tuop_110 普通tile指令
        
        for (int i = 0; i < 8; i++) begin
            result = is_sync_or_wait_instruction(test_instructions[i]);
            
            // 确定指令类型
            if (test_instructions[i][6:0] != 7'b1111011) begin
                instr_type = "Non-tile";
            end else begin
                case (test_instructions[i][14:12])
                    3'b000: instr_type = "Memory";
                    3'b001: instr_type = "Matrix";
                    3'b010: instr_type = "Vector";
                    3'b011: instr_type = "Other";
                    3'b100: instr_type = "CSR";
                    3'b101: instr_type = "Sync";
                    3'b110: instr_type = "Multi-lane";
                    3'b111: instr_type = "ACE";
                    default: instr_type = "Unknown";
                endcase
            end
            
            $display("Test %0d: 0x%08x (%s) -> %s", 
                    i, test_instructions[i], instr_type, 
                    result ? "SYNC/WAIT" : "NOT SYNC/WAIT");
        end
        
        $display("\n=== Test Complete ===");
        $finish;
    end

endmodule
