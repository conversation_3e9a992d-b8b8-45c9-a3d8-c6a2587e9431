// Checker模块示例 - 使用可配置ISQ监控
// 展示如何自定义配置并include ISQ监控代码

module checker_configurable;

    // ========================================
    // 您现有的checker代码...
    // ========================================
    
    integer error_count = 0;
    integer check_count = 0;
    
    initial begin
        $display("Configurable Checker module started");
        wait(`PE_TOP.pe_resetn);
        $display("Checker: Reset released, starting checks...");
    end
    
    always @(posedge `PE_TOP.clk) begin
        if (`PE_TOP.pe_resetn) begin
            check_count = check_count + 1;
            // 您的检查逻辑...
        end
    end
    
    // ========================================
    // ISQ监控配置 - 在include之前定义
    // ========================================
    
    // 自定义配置参数
    `define ISQ_DUMP_INTERVAL 500           // 每500拍dump一次（而不是默认的1000）
    `define ISQ_MAX_COUNT 64                // 队列大小为64（而不是默认的32）
    `define ISQ_OUTPUT_FILE "custom_isq.log" // 自定义输出文件名
    // `define ISQ_DEBUG_OFF                // 取消注释可关闭调试输出
    
    // 如果您的设计层次不同，可以自定义路径
    // `define ISQ_TOP_PATH `MY_TOP
    // `define ISQ_MODULE_PATH `MY_TOP.my_path.to.isq
    
    // Include ISQ监控功能
    `include "isq_monitor_configurable.sv"
    
    // ========================================
    // 您的其他checker代码继续...
    // ========================================
    
    final begin
        $display("Configurable Checker Summary:");
        $display("  Total checks performed: %0d", check_count);
        $display("  Total errors found: %0d", error_count);
    end

endmodule

// ========================================
// 另一个示例：最小配置的checker
// ========================================

module simple_checker;
    
    // 最简单的使用方式 - 使用所有默认配置
    `include "isq_monitor_configurable.sv"
    
    // 您的其他代码...
    initial begin
        $display("Simple checker with default ISQ monitoring");
    end

endmodule

// ========================================
// 第三个示例：多个ISQ监控实例
// ========================================

module multi_isq_checker;
    
    // 监控第一个PE的ISQ
    `define ISQ_DUMP_INTERVAL 1000
    `define ISQ_MAX_COUNT 32
    `define ISQ_OUTPUT_FILE "pe0_isq.log"
    `define ISQ_TOP_PATH `PE_TOP
    `define ISQ_MODULE_PATH `PE_TOP.u_tcore.tc_tfe.isq
    `include "isq_monitor_configurable.sv"
    
    // 如果需要监控多个PE，可以创建多个文件或使用不同的变量名
    // 注意：当前版本不支持多实例，需要修改变量名来避免冲突
    
    initial begin
        $display("Multi-ISQ checker started");
    end

endmodule
