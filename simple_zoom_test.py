#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的缩放功能测试脚本
"""

import os
import webbrowser

def check_html_zoom_implementation():
    """检查HTML文件中的缩放实现"""
    html_file = 'itrace_timeline.html'
    
    if not os.path.exists(html_file):
        print("❌ HTML文件不存在，请先运行 itrace_timeline_visualizer.py")
        return False
    
    print("检查HTML文件中的缩放实现...")
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查关键的缩放功能代码
    checks = [
        ('currentZoom变量', 'let currentZoom = 1.0;'),
        ('缩放滑块', 'id="zoomSlider"'),
        ('缩放值显示', 'id="zoomValue"'),
        ('updateZoom函数', 'function updateZoom()'),
        ('calculateBarPosition函数', 'function calculateBarPosition(start, end)'),
        ('缩放因子应用', '* currentZoom'),
        ('事件监听器', 'addEventListener(\'input\', updateZoom)'),
        ('重新渲染调用', 'initTimeAxis()'),
        ('时间轴宽度更新', 'timeAxis.style.minWidth'),
    ]
    
    all_passed = True
    
    for check_name, check_pattern in checks:
        if check_pattern in content:
            print(f"✅ {check_name}: 找到")
        else:
            print(f"❌ {check_name}: 未找到 - {check_pattern}")
            all_passed = False
    
    return all_passed

def manual_test_instructions():
    """提供详细的手动测试说明"""
    print("\n" + "="*70)
    print("🧪 缩放功能手动测试指南")
    print("="*70)
    print()
    print("📋 测试步骤:")
    print("-"*40)
    print("1. 打开生成的 itrace_timeline.html 文件")
    print("2. 找到页面顶部的控制面板，其中包含缩放滑块")
    print("3. 进行以下测试:")
    print()
    
    print("🔍 基本缩放测试:")
    print("   • 拖动缩放滑块到不同位置（0.1x - 5.0x）")
    print("   • 观察缩放值显示是否实时更新")
    print("   • 检查时间线条是否随缩放变化宽度")
    print()
    
    print("📏 视觉效果验证:")
    print("   • 缩放 > 1.0x: 时间线应该变宽，可能出现水平滚动条")
    print("   • 缩放 < 1.0x: 时间线应该变窄，内容更紧凑")
    print("   • 时间轴刻度间距应该随缩放调整")
    print("   • 指令执行阶段的条形图应该相应拉伸/压缩")
    print()
    
    print("🔄 功能完整性测试:")
    print("   • 在不同缩放级别下，鼠标悬停工具提示应该正常工作")
    print("   • 过滤和排序功能应该在缩放状态下正常工作")
    print("   • 点击'重置视图'按钮应该将缩放恢复到1.0x")
    print()
    
    print("✅ 预期结果:")
    print("   • 缩放滑块拖动应该流畅响应")
    print("   • 缩放值显示应该准确反映当前缩放级别")
    print("   • 时间线宽度应该与缩放值成正比")
    print("   • 所有交互功能在缩放状态下都应该正常工作")
    print()
    
    print("🐛 如果发现问题:")
    print("   • 缩放滑块不响应 → 检查JavaScript控制台错误")
    print("   • 时间线不变化 → 可能是CSS样式问题")
    print("   • 缩放值不更新 → 检查DOM元素ID是否正确")
    print("="*70)

def main():
    """主函数"""
    print("🔧 ITrace Timeline 缩放功能修复验证")
    print("="*50)
    
    # 检查HTML实现
    if check_html_zoom_implementation():
        print("\n✅ HTML文件中的缩放实现检查通过!")
        print("🎯 修复内容包括:")
        print("   • 修复了calculateBarPosition函数，加入currentZoom因子")
        print("   • 修复了updateZoom函数，重新渲染时间轴和指令")
        print("   • 修复了initTimeAxis函数，支持缩放时的时间轴调整")
        print("   • 添加了时间轴容器的宽度动态调整")
    else:
        print("\n❌ HTML文件中的缩放实现有问题!")
        return
    
    # 提供手动测试说明
    manual_test_instructions()
    
    # 尝试打开HTML文件
    html_file = 'itrace_timeline.html'
    if os.path.exists(html_file):
        try:
            file_path = f'file://{os.path.abspath(html_file)}'
            webbrowser.open(file_path)
            print(f"\n🌐 已在浏览器中打开文件进行测试:")
            print(f"   {file_path}")
        except Exception as e:
            print(f"\n⚠️  无法自动打开浏览器: {e}")
            print(f"请手动打开: {os.path.abspath(html_file)}")
    
    print(f"\n🎉 缩放功能修复完成! 请按照上述指南进行测试。")

if __name__ == "__main__":
    main()
