# ISQ Monitor - 快速开始指南

## 概述

ISQ Monitor是一个SystemVerilog模块，用于监控PE_TOP.u_tcore.tc_tfe.isq模块中的cisq_vld_cnt和misq_vld_cnt计数器。它在dispatch时统计这些计数器的值，并每1000个时钟周期dump一次数据。

## 文件列表

### 模块版本
- `isq_monitor.sv` - 主监控模块（带端口版本，推荐）
- `isq_monitor_no_ports.sv` - 无端口版本（使用层次化引用）

### Include版本（最简单）
- `isq_monitor_include.sv` - 基础include版本
- `isq_monitor_configurable.sv` - 可配置include版本（推荐）

### 文档和示例
- `isq_monitor_usage.md` - 详细使用说明
- `ISQ_Monitor_Include_Guide.md` - Include版本使用指南
- `testbench_integration_example.sv` - 模块版本集成示例
- `checker_with_isq_monitor.sv` - Include版本集成示例
- `README_ISQ_Monitor.md` - 本文件（快速开始指南）

## 三种使用方式

### 方式1：Include版本（最推荐）⭐⭐⭐⭐⭐

**优点**: 极简集成，直接访问信号，高度可配置
**缺点**: 使用全局变量，不支持多实例

```systemverilog
module your_checker;
    // 您现有的代码...

    // 方式1：直接include - 就这一行！
    `include "isq_monitor_configurable.sv"

    // 您的其他代码...
endmodule
```

### 方式2：带端口模块版本

**优点**: 更清晰的接口，更好的模块化，便于重用
**缺点**: 需要显式连接端口

```systemverilog
module your_testbench;
    // 您现有的代码...

    // 方式2：显式端口连接
    isq_monitor u_isq_monitor(
        .clk                (`PE_TOP.clk),
        .pe_resetn          (`PE_TOP.pe_resetn),
        .cisq_vld_cnt       (`PE_TOP.u_tcore.tc_tfe.isq.cisq_vld_cnt),
        .misq_vld_cnt       (`PE_TOP.u_tcore.tc_tfe.isq.misq_vld_cnt),
        .idu_isq_uop0_valid (`PE_TOP.u_tcore.tc_tfe.isq.idu_isq_uop0_valid),
        .idu_isq_uop1_valid (`PE_TOP.u_tcore.tc_tfe.isq.idu_isq_uop1_valid)
    );

    // 您的其他代码...
endmodule
```

### 方式3：无端口模块版本

**优点**: 简单的模块集成
**缺点**: 依赖层次化引用，不够灵活

```systemverilog
module your_testbench;
    // 您现有的代码...

    // 方式3：无端口模块
    isq_monitor_no_ports u_isq_monitor();

    // 您的其他代码...
endmodule
```

## 三种方式对比

| 特性 | Include版本 | 带端口模块 | 无端口模块 |
|------|-------------|------------|------------|
| 文件名 | `isq_monitor_configurable.sv` | `isq_monitor.sv` | `isq_monitor_no_ports.sv` |
| 集成复杂度 | 极简（一行include） | 中等（需要连接端口） | 简单（一行实例化） |
| 配置灵活性 | 高（宏定义配置） | 中等（参数配置） | 低（固定配置） |
| 多实例支持 | 差（变量冲突） | 好（独立实例） | 好（独立实例） |
| 重用性 | 好（任何模块可用） | 好（可用于不同设计） | 差（绑定特定层次） |
| 调试难度 | 容易（直接访问） | 容易（端口明确） | 中等（需要检查层次） |
| 推荐程度 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ | ⭐⭐⭐ |

### 编译和运行

```bash
# 方式1：使用带端口版本
vlog isq_monitor.sv your_testbench.sv

# 方式2：使用无端口版本
vlog isq_monitor_no_ports.sv your_testbench.sv

# 运行
vsim your_testbench
run -all
```

### 3. 查看结果

仿真完成后，检查生成的 `isq_monitor.log` 文件：

```bash
cat isq_monitor.log
```

## 输出示例

```csv
Time,CycleCount,CISQ_VLD_CNT,MISQ_VLD_CNT,CISQ_Utilization,MISQ_Utilization,Dispatch_Port0,Dispatch_Port1
10000,1000,15,8,46.88,25.00,1,0
20000,2000,20,12,62.50,37.50,0,1
30000,3000,25,16,78.13,50.00,1,1
```

## 主要功能

✅ **自动监控**: 无需手动配置，自动连接到ISQ信号  
✅ **定期dump**: 每1000周期自动输出数据  
✅ **利用率计算**: 自动计算队列利用率百分比  
✅ **满值告警**: 队列满时自动输出警告  
✅ **CSV格式**: 便于Excel/Python分析  
✅ **调试信息**: 可选的控制台调试输出  

## 配置选项

### 修改dump间隔

```systemverilog
// 在isq_monitor.sv中修改这一行
integer dump_interval = 500;  // 改为每500周期dump一次
```

### 修改最大计数值

```systemverilog
// 如果您的队列大小不是32，请修改
parameter MAX_COUNT = 64;  // 改为64
```

### 关闭调试输出

```systemverilog
// 关闭控制台调试信息
parameter DEBUG_ENABLE = 0;
```

## 信号路径检查

如果您的设计层次结构与默认不同，请修改信号连接：

```systemverilog
// 默认路径
assign cisq_vld_cnt = `PE_TOP.u_tcore.tc_tfe.isq.cisq_vld_cnt;

// 如果您的路径不同，请修改为正确的路径
assign cisq_vld_cnt = `YOUR_TOP.your_path.to.isq.cisq_vld_cnt;
```

## 常见问题

### Q: 我应该选择哪种版本？
**A**:
- **新项目或规范的testbench**: 推荐使用带端口版本（`isq_monitor.sv`）
- **快速验证或简单集成**: 可以使用无端口版本（`isq_monitor_no_ports.sv`）

### Q: 编译时出现信号未找到错误
**A**:
- **带端口版本**: 检查端口连接中的信号路径是否正确
- **无端口版本**: 检查模块内部的层次化引用路径是否正确

### Q: clk和pe_resetn不需要和外部连接就能工作吗？
**A**:
- **带端口版本**: 需要显式连接，更清晰可控
- **无端口版本**: 通过层次化引用自动连接，但依赖特定的设计层次

### Q: 文件无法生成
**A**: 检查文件权限和磁盘空间，确保仿真器有写入权限。

### Q: 数据看起来不对
**A**: 确认计数器位宽和最大值设置是否与您的设计匹配。

### Q: 仿真变慢了
**A**: 可以增加dump间隔或关闭DEBUG_ENABLE来提高性能。

## 数据分析

### 使用Excel
1. 打开 `isq_monitor.log` 文件
2. 选择"数据" -> "从文本/CSV"
3. 创建图表分析利用率趋势

### 使用Python
```python
import pandas as pd
import matplotlib.pyplot as plt

# 读取数据
df = pd.read_csv('isq_monitor.log')

# 绘制利用率图
plt.plot(df['CycleCount'], df['CISQ_Utilization'], label='CISQ')
plt.plot(df['CycleCount'], df['MISQ_Utilization'], label='MISQ')
plt.xlabel('Cycles')
plt.ylabel('Utilization (%)')
plt.legend()
plt.show()
```

## 高级用法

### 1. 多个监控实例
如果需要监控多个PE，可以创建多个实例：

```systemverilog
isq_monitor u_isq_monitor_pe0();  // 监控PE0
isq_monitor u_isq_monitor_pe1();  // 监控PE1
```

### 2. 自定义输出文件名
修改 `isq_monitor.sv` 中的文件名：

```systemverilog
isq_monitor_file = $fopen("pe0_isq_monitor.log", "w");
```

### 3. 添加更多统计信息
可以在 `dump_isq_status()` 任务中添加更多字段。

## 技术支持

如需更详细的信息，请参考：
- `isq_monitor_usage.md` - 完整使用说明
- `testbench_integration_example.sv` - 集成示例

## 版本信息

- 版本: 1.0
- 创建日期: 2025-07-28
- 兼容性: SystemVerilog IEEE 1800-2017
