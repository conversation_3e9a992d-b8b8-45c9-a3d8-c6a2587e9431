# 指令断流统计功能说明

## 功能概述

新增的指令断流统计功能可以统计在tile指令执行期间，指令流水线断流的时间。

## 统计范围

- **开始时间**: OPCODE为`0x9d02`（汇编指令`c.jalr s10`）的取指时间
- **结束时间**: `PE_TOP.u_tcore.tc_tfe.ibuf.tfe_csr.t0_csr_tbl.tcore2bd_thread_done`信号拉高
- **断流定义**: `ifu_i0_valid`和`ifu_i1_valid`同时为0的状态

## 统计指标

1. **断流时间**: 在监控期间，指令断流的总时间
2. **监控开始时间**: OPCODE为`0x9d02`指令的取指时间
3. **监控结束时间**: `thread_done`信号拉高的时间
4. **总监控时间**: 从监控开始到结束的总时间
5. **断流百分比**: 断流时间占总监控时间的百分比
6. **tile指令总数**: 统计期间检测到的tile指令数量

## 输出信息

### 在itrace.log文件末尾输出：

```
// ===== INSTRUCTION STALL STATISTICS =====
// Stall monitor start time (0x9d02 fetch): 1000.00
// Stall monitor end time (thread_done): 5000.00
// Total monitor time: 4000.00
// Total stall time: 800.00
// Stall percentage: 20.00%
// Total tile instructions: 10
```

### 在控制台输出：

```
=== INSTRUCTION STALL STATISTICS ===
Stall monitor start time (0x9d02 fetch): 1000.00
Stall monitor end time (thread_done): 5000.00
Total monitor time: 4000.00
Total stall time: 800.00
Stall percentage: 20.00%
Total tile instructions: 10
```

## 实现细节

### 新增信号和变量

- `instruction_stall`: 断流检测信号
- `thread_done`: 线程完成信号
- `stall_monitor_start_time`: 断流监控开始时间（0x9d02指令取指时间）
- `stall_monitor_end_time`: 断流监控结束时间（thread_done时间）
- `total_monitor_time`: 总监控时间
- `total_stall_time`: 累计断流时间
- `stall_monitor_active`: 断流监控期间标志
- `stall_in_progress`: 当前断流状态
- `monitor_started`: 是否已经开始监控
- `total_tile_count`: tile指令总数
- `START_OPCODE`: 开始监控的指令opcode（0x9d02）

### 关键逻辑

1. **断流检测**: `assign instruction_stall = (!ifu_i0_valid && !ifu_i1_valid) && pe_resetn;`

2. **线程完成检测**: `assign thread_done = PE_TOP.u_tcore.tc_tfe.ibuf.tfe_csr.t0_csr_tbl.tcore2bd_thread_done;`

3. **开始统计**: 当检测到OPCODE为`0x9d02`的指令时，设置`stall_monitor_active = 1`

4. **断流时间累计**: 在监控期间，检测断流开始和结束，累计断流时间

5. **结束统计**: 当`thread_done`信号拉高时，记录结束时间，计算总监控时间和断流百分比

6. **百分比计算**: `stall_percentage = (total_stall_time * 100.0) / total_monitor_time`

## 调试信息

如果启用DEBUG模式（`DEBUG_ENABLE = 1`），会在debug日志中输出详细的断流检测信息：

- 断流开始时间
- 断流结束时间和持续时间
- 0x9d02指令检测时间
- thread_done信号检测时间

## 使用方法

1. 确保`DEBUG_ENABLE`参数设置正确（1启用debug，0禁用）
2. 运行仿真
3. 查看`itrace.log`文件末尾的统计信息
4. 查看控制台输出的汇总信息

## 注意事项

- 只有在检测到OPCODE为`0x9d02`的指令后才会进行断流统计
- 如果没有检测到`0x9d02`指令，会输出"Start opcode 0x9d02 not found"
- 断流统计只在复位释放后进行
- 统计在`thread_done`信号拉高时结束
- 统计结果会在仿真结束时自动输出
- `0x9d02`对应的汇编指令是`c.jalr s10`
