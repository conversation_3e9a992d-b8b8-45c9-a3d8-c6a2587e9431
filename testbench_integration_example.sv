// Testbench集成示例
// 展示如何在现有testbench中集成isq_monitor模块

// 假设这是您现有的testbench文件
module your_testbench;

    // 您现有的testbench代码...
    // (这里只是示例，实际使用时请保留您的原有代码)
    
    // 时钟和复位
    logic clk;
    logic resetn;
    
    // 时钟生成
    initial begin
        clk = 0;
        forever #5 clk = ~clk;  // 10ns周期
    end
    
    // 复位序列
    initial begin
        resetn = 0;
        #100;
        resetn = 1;
        $display("Reset released at time %0t", $realtime);
    end
    
    // ========================================
    // ISQ监控模块集成 - 方式1：显式端口连接（推荐）
    // ========================================
    isq_monitor u_isq_monitor(
        .clk                (clk),  // 或者 `PE_TOP.clk
        .pe_resetn          (resetn), // 或者 `PE_TOP.pe_resetn
        .cisq_vld_cnt       (`PE_TOP.u_tcore.tc_tfe.isq.cisq_vld_cnt),
        .misq_vld_cnt       (`PE_TOP.u_tcore.tc_tfe.isq.misq_vld_cnt),
        .idu_isq_uop0_valid (`PE_TOP.u_tcore.tc_tfe.isq.idu_isq_uop0_valid),
        .idu_isq_uop1_valid (`PE_TOP.u_tcore.tc_tfe.isq.idu_isq_uop1_valid)
    );

    // ========================================
    // 或者方式2：如果您的testbench没有这些信号，可以直接连接到顶层
    // ========================================
    /*
    isq_monitor u_isq_monitor(
        .clk                (`PE_TOP.clk),
        .pe_resetn          (`PE_TOP.pe_resetn),
        .cisq_vld_cnt       (`PE_TOP.u_tcore.tc_tfe.isq.cisq_vld_cnt),
        .misq_vld_cnt       (`PE_TOP.u_tcore.tc_tfe.isq.misq_vld_cnt),
        .idu_isq_uop0_valid (`PE_TOP.u_tcore.tc_tfe.isq.idu_isq_uop0_valid),
        .idu_isq_uop1_valid (`PE_TOP.u_tcore.tc_tfe.isq.idu_isq_uop1_valid)
    );
    */
    
    // 您的其他testbench代码...
    // 例如：激励生成、检查器等
    
    // 仿真控制
    initial begin
        // 等待复位释放
        wait(resetn);
        
        // 运行一段时间来观察ISQ监控
        #1000000;  // 运行1ms
        
        $display("Simulation completed");
        $finish;
    end
    
    // 可选：添加一些简单的激励来触发dispatch活动
    // （这只是示例，实际使用时请根据您的设计调整）
    /*
    initial begin
        wait(resetn);
        
        // 模拟一些dispatch活动
        repeat(1000) begin
            @(posedge clk);
            // 这里可以添加一些激励代码
        end
    end
    */

endmodule

// ========================================
// 编译和运行脚本示例 (compile_and_run.do)
// ========================================
/*
# ModelSim/QuestaSim脚本示例
# 将以下内容保存为 compile_and_run.do 文件

# 编译文件
vlog isq_monitor.sv
vlog testbench_integration_example.sv
# 如果有其他设计文件，也要编译
# vlog your_design_files.sv

# 启动仿真
vsim your_testbench

# 添加波形（可选）
add wave -radix hex /your_testbench/u_isq_monitor/*

# 运行仿真
run -all

# 查看结果
echo "ISQ monitoring results:"
exec cat isq_monitor.log

quit
*/

// ========================================
// Makefile示例
// ========================================
/*
# 将以下内容保存为 Makefile

# 定义变量
VLOG = vlog
VSIM = vsim
SV_FILES = isq_monitor.sv testbench_integration_example.sv
TOP_MODULE = your_testbench

# 默认目标
all: compile simulate

# 编译目标
compile:
	$(VLOG) $(SV_FILES)

# 仿真目标
simulate:
	$(VSIM) -c -do "run -all; quit" $(TOP_MODULE)

# 清理目标
clean:
	rm -rf work transcript vsim.wlf *.log

# 查看结果
view_results:
	@echo "=== ISQ Monitor Results ==="
	@cat isq_monitor.log | head -20
	@echo "..."
	@echo "=== Total lines: ==="
	@wc -l isq_monitor.log

.PHONY: all compile simulate clean view_results
*/
