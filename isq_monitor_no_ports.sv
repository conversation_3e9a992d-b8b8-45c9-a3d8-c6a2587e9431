// ISQ Valid Count Monitor - 无端口版本
// 监控PE_TOP.u_tcore.tc_tfe.isq.cisq_vld_cnt和misq_vld_cnt的值
// 在dispatch的时候统计，每1000拍dump一次数据
// 这个版本使用层次化引用，不需要端口连接

module isq_monitor_no_ports;

    // 时钟和复位信号（通过层次化引用获取）
    logic clk, pe_resetn;
    
    // ISQ计数器信号（通过层次化引用获取）
    logic [5:0] cisq_vld_cnt;  // 假设6位宽，满值为32 (2^5)
    logic [5:0] misq_vld_cnt;  // 假设6位宽，满值为32 (2^5)
    
    // Dispatch信号（通过层次化引用获取）
    logic idu_isq_uop0_valid, idu_isq_uop1_valid;
    
    // 统计变量
    integer cycle_count = 0;
    integer dump_interval = 1000;  // 每1000拍dump一次
    
    // 文件句柄
    integer isq_monitor_file;
    
    // 参数定义
    parameter MAX_COUNT = 32;  // 满值为32
    parameter DEBUG_ENABLE = 1;  // 调试开关
    
    // 信号连接 - 使用层次化引用
    assign clk = `PE_TOP.clk;
    assign pe_resetn = `PE_TOP.pe_resetn;
    
    // 连接ISQ计数器信号
    assign cisq_vld_cnt = `PE_TOP.u_tcore.tc_tfe.isq.cisq_vld_cnt;
    assign misq_vld_cnt = `PE_TOP.u_tcore.tc_tfe.isq.misq_vld_cnt;
    
    // 连接dispatch信号
    assign idu_isq_uop0_valid = `PE_TOP.u_tcore.tc_tfe.isq.idu_isq_uop0_valid;
    assign idu_isq_uop1_valid = `PE_TOP.u_tcore.tc_tfe.isq.idu_isq_uop1_valid;
    
    // 初始化
    initial begin
        // 打开输出文件
        isq_monitor_file = $fopen("isq_monitor.log", "w");
        if (isq_monitor_file == 0) begin
            $display("ERROR: Cannot open isq_monitor.log file");
            $finish;
        end
        
        // 写入CSV头部
        $fwrite(isq_monitor_file, "Time,CycleCount,CISQ_VLD_CNT,MISQ_VLD_CNT,CISQ_Utilization,MISQ_Utilization,Dispatch_Port0,Dispatch_Port1\n");
        
        $display("ISQ Monitor (No Ports) initialized - monitoring cisq_vld_cnt and misq_vld_cnt");
        $display("Output file: isq_monitor.log");
        $display("Dump interval: %0d cycles", dump_interval);
        $display("Max count value: %0d", MAX_COUNT);
    end
    
    // 主监控逻辑
    always @(posedge clk) begin
        // 复位期间不进行统计
        if (!pe_resetn) begin
            cycle_count <= 0;
            if (DEBUG_ENABLE) begin
                $display("Time: %0t - ISQ Monitor Reset", $realtime);
            end
        end else begin
            // 增加周期计数
            cycle_count <= cycle_count + 1;
            
            // 每1000拍dump一次数据
            if (cycle_count % dump_interval == 0) begin
                dump_isq_status();
            end
            
            // 检查dispatch事件（可选的额外监控）
            if (idu_isq_uop0_valid || idu_isq_uop1_valid) begin
                if (DEBUG_ENABLE && (cycle_count % 100 == 0)) begin  // 每100拍输出一次dispatch信息
                    $display("Time: %0t - Dispatch Event: Port0=%b, Port1=%b, CISQ=%0d, MISQ=%0d", 
                            $realtime, idu_isq_uop0_valid, idu_isq_uop1_valid, cisq_vld_cnt, misq_vld_cnt);
                end
            end
        end
    end
    
    // 任务：dump ISQ状态
    task dump_isq_status();
        real cisq_utilization, misq_utilization;
        
        // 计算利用率（百分比）
        cisq_utilization = (real'(cisq_vld_cnt) / real'(MAX_COUNT)) * 100.0;
        misq_utilization = (real'(misq_vld_cnt) / real'(MAX_COUNT)) * 100.0;
        
        // 写入CSV格式数据
        $fwrite(isq_monitor_file, "%0t,%0d,%0d,%0d,%.2f,%.2f,%b,%b\n",
                $realtime, cycle_count, cisq_vld_cnt, misq_vld_cnt,
                cisq_utilization, misq_utilization,
                idu_isq_uop0_valid, idu_isq_uop1_valid);
        
        // 控制台输出（可选）
        if (DEBUG_ENABLE) begin
            $display("Time: %0t - ISQ Status: CISQ=%0d/%0d(%.1f%%), MISQ=%0d/%0d(%.1f%%), Cycle=%0d",
                    $realtime, cisq_vld_cnt, MAX_COUNT, cisq_utilization,
                    misq_vld_cnt, MAX_COUNT, misq_utilization, cycle_count);
        end
        
        // 检查满值情况
        if (cisq_vld_cnt == MAX_COUNT) begin
            $display("WARNING: CISQ is full (%0d/%0d) at time %0t", cisq_vld_cnt, MAX_COUNT, $realtime);
        end
        
        if (misq_vld_cnt == MAX_COUNT) begin
            $display("WARNING: MISQ is full (%0d/%0d) at time %0t", misq_vld_cnt, MAX_COUNT, $realtime);
        end
        
        // 刷新文件缓冲区
        $fflush(isq_monitor_file);
    endtask
    
    // 仿真结束时的清理
    final begin
        // 最后一次dump
        if (cycle_count > 0) begin
            $display("Final ISQ status dump at simulation end:");
            dump_isq_status();
        end
        
        // 关闭文件
        if (isq_monitor_file != 0) begin
            $fclose(isq_monitor_file);
        end
        
        // 输出统计摘要
        $display("ISQ Monitor Summary:");
        $display("  Total cycles monitored: %0d", cycle_count);
        $display("  Total dumps: %0d", (cycle_count / dump_interval) + 1);
        $display("  Output file: isq_monitor.log");
    end

endmodule
