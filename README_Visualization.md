# ISQ Monitor Visualization Tools

## Quick Start Guide

### Problem: Non-ASCII character error
If you encounter "Non-ASCII character" errors, use the **simple_plot.py** script which is designed to avoid encoding issues.

### Recommended Usage

#### 1. Simple Plotting (Recommended)
```bash
python simple_plot.py
```
- No encoding issues
- Easy to use
- Supports both separate and combined plots

#### 2. Generate Test Data (For Testing)
```bash
python generate_test_data.py
python simple_plot.py test_isq_monitor.log
```

## File Overview

### Working Scripts (No Encoding Issues)
- **`simple_plot.py`** - Main plotting script (RECOMMENDED)
- **`generate_test_data.py`** - Generate test data for testing

### Advanced Scripts (May have encoding issues on some systems)
- **`quick_plot.py`** - Feature-rich plotting (fixed encoding)
- **`realtime_monitor.py`** - Real-time monitoring (fixed encoding)
- **`isq_visualizer.py`** - Full-featured visualizer (fixed encoding)

## Installation

```bash
# Install required packages
pip install pandas matplotlib

# Or using conda
conda install pandas matplotlib
```

## Usage Examples

### Basic Usage
```bash
# Use default file (isq_monitor.log)
python simple_plot.py

# Use custom file
python simple_plot.py my_isq_data.log
```

### Interactive Mode
```bash
python simple_plot.py
# Choose plot type:
# 1. Separate plots (default)
# 2. Combined plot
```

### Generate and Test
```bash
# Generate test data
python generate_test_data.py

# Plot test data
python simple_plot.py test_isq_monitor.log
```

## Expected Output

### Console Output
```
ISQ Utilization Plotter
==============================
Reading file: isq_monitor.log
Loaded 125 data points

=== ISQ Utilization Statistics ===
Time range: 10 - 1250
Total cycles: 1,250
Data points: 125

CISQ Utilization:
  Average: 45.67%
  Maximum: 98.50%
  Minimum: 2.10%

MISQ Utilization:
  Average: 38.23%
  Maximum: 87.30%
  Minimum: 0.00%

Plot generated successfully!
```

### Graph Features
- **Separate Plots**: CISQ and MISQ in separate subplots
- **Combined Plot**: Both utilizations on same chart with reference lines
- **Statistics Boxes**: Show average, max, min values
- **Grid Lines**: For easy reading
- **Legends**: Clear labeling

## Troubleshooting

### Common Issues

#### 1. "Non-ASCII character" error
**Solution**: Use `simple_plot.py` instead of other scripts
```bash
python simple_plot.py
```

#### 2. "File not found" error
**Solution**: 
- Check if ISQ monitor is running
- Generate test data for testing
```bash
python generate_test_data.py
python simple_plot.py test_isq_monitor.log
```

#### 3. "Module not found" error
**Solution**: Install required packages
```bash
pip install pandas matplotlib
```

#### 4. Empty or corrupted data file
**Solution**: Check file format
```bash
# File should start with:
# Time,CycleCount,CISQ_VLD_CNT,MISQ_VLD_CNT,CISQ_Utilization,MISQ_Utilization
head isq_monitor.log
```

### Python Version Issues
- Requires Python 3.6 or higher
- If using older Python, upgrade or use Python 3 explicitly:
```bash
python3 simple_plot.py
```

## File Format

The ISQ monitor should generate CSV files with this format:
```csv
Time,CycleCount,CISQ_VLD_CNT,MISQ_VLD_CNT,CISQ_Utilization,MISQ_Utilization
100,10,5,3,15.63,9.38
200,20,8,6,25.00,18.75
300,30,12,9,37.50,28.13
```

## Advanced Features

### Command Line Arguments
```bash
# Specify custom file
python simple_plot.py /path/to/your/data.log

# Generate custom test data
python generate_test_data.py custom_test.log 1000
```

### Customization
You can modify `simple_plot.py` to:
- Change colors and styles
- Add more statistics
- Modify plot layout
- Save plots to files

### Example Modifications
```python
# Save plot instead of showing
plt.savefig('isq_utilization.png', dpi=300, bbox_inches='tight')

# Change colors
plt.plot(df['Time'], df['CISQ_Utilization'], 'g-', ...)  # Green line

# Add more reference lines
plt.axhline(y=90, color='red', linestyle=':', alpha=0.7, label='90%')
```

## Integration with Simulation

### Workflow
1. Start ISQ monitor in your testbench:
   ```systemverilog
   `include "isq_monitor_include.sv"
   ```

2. Run simulation to generate `isq_monitor.log`

3. Visualize results:
   ```bash
   python simple_plot.py
   ```

### Batch Processing
```bash
# Process multiple files
for file in pe*.log; do
    python simple_plot.py "$file"
done
```

## Performance Tips

- For large files (>10,000 points), consider sampling
- Use combined plot for quick overview
- Use separate plots for detailed analysis
- Generate test data to verify scripts work before real simulation

## Support

If you continue to have encoding issues:
1. Use `simple_plot.py` (most compatible)
2. Check your Python installation
3. Try running with explicit encoding:
   ```bash
   PYTHONIOENCODING=utf-8 python simple_plot.py
   ```
4. Use Python 3 explicitly if you have both Python 2 and 3
