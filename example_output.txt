修改后的itrace.log输出格式示例：

CSV头部：
InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime

示例数据行：
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1025,1030,1035,1040,1045
3,0x000000001010,0x00000000fb0012ab,"twait",1050,1055,0,0,0
4,0x000000001014,0x00000000fb801000,"ace_bsync x0",1060,1065,0,0,0
5,0x000000001018,0x00000000fb345678,"tcsrw.i 0x5",1070,1075,1080,1085,1090

说明：
- 第4列现在包含了反编译的指令字符串，用双引号包围
- 反编译字符串显示了指令的助记符和操作数
- 对于tile指令，会显示具体的操作类型（如tld、tmma、tcsrw等）
- 对于sync/wait指令（如twait、ace_bsync等），只有FetchStartTime和DecodeStartTime，后续时间为0
- 对于非tile指令，会显示"unknown_non_tile"

sync/wait指令的特殊处理：
- 这些指令在fetch完成后立即写入日志文件
- 只记录FetchStartTime和DecodeStartTime
- DispatchStartTime、ExecuteStartTime、ExecuteEndTime都为0
- 不会进入指令队列，直接丢弃

未完成指令的输出格式也会包含反编译信息：
// Incomplete Instr: ID=0, PC=0x000000001000, Instr=0x00000000fb123456, Disasm="tld.trii.linear.u32.global t5, (x10)" - STUCK in EXECUTE stage
