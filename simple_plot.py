#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple ISQ Utilization Plotter
A simple script to plot ISQ utilization from monitor log file

Usage:
python simple_plot.py [filename]
"""

import pandas as pd
import matplotlib.pyplot as plt
import os
import sys

def plot_isq_data(filename='isq_monitor.log'):
    """Plot ISQ utilization data from CSV file"""
    
    # Check if file exists
    if not os.path.exists(filename):
        print(f"Error: File '{filename}' not found")
        print("Make sure the ISQ monitor is running and generating the log file")
        return False
    
    try:
        # Read CSV file
        print(f"Reading file: {filename}")
        df = pd.read_csv(filename)
        
        if df.empty:
            print("Warning: File is empty")
            return False
        
        print(f"Loaded {len(df)} data points")
        
        # Create figure with subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 8))
        
        # Plot CISQ utilization
        ax1.plot(df['Time'], df['CISQ_Utilization'], 'b-', linewidth=2, 
                marker='o', markersize=2, label='CISQ Utilization')
        ax1.set_ylabel('CISQ Utilization (%)')
        ax1.set_title('ISQ Utilization Over Simulation Time', fontweight='bold')
        ax1.grid(True, alpha=0.3)
        ax1.set_ylim(0, 100)
        ax1.legend()
        
        # Add CISQ statistics
        cisq_avg = df['CISQ_Utilization'].mean()
        cisq_max = df['CISQ_Utilization'].max()
        cisq_min = df['CISQ_Utilization'].min()
        stats_text = f'Avg: {cisq_avg:.1f}%\nMax: {cisq_max:.1f}%\nMin: {cisq_min:.1f}%'
        ax1.text(0.02, 0.95, stats_text, transform=ax1.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))
        
        # Plot MISQ utilization
        ax2.plot(df['Time'], df['MISQ_Utilization'], 'r-', linewidth=2, 
                marker='s', markersize=2, label='MISQ Utilization')
        ax2.set_xlabel('Simulation Time')
        ax2.set_ylabel('MISQ Utilization (%)')
        ax2.grid(True, alpha=0.3)
        ax2.set_ylim(0, 100)
        ax2.legend()
        
        # Add MISQ statistics
        misq_avg = df['MISQ_Utilization'].mean()
        misq_max = df['MISQ_Utilization'].max()
        misq_min = df['MISQ_Utilization'].min()
        stats_text = f'Avg: {misq_avg:.1f}%\nMax: {misq_max:.1f}%\nMin: {misq_min:.1f}%'
        ax2.text(0.02, 0.95, stats_text, transform=ax2.transAxes, 
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightcoral', alpha=0.8))
        
        plt.tight_layout()
        
        # Print statistics
        print("\n=== ISQ Utilization Statistics ===")
        print(f"Time range: {df['Time'].min()} - {df['Time'].max()}")
        print(f"Total cycles: {df['CycleCount'].max():,}")
        print(f"Data points: {len(df):,}")
        print(f"\nCISQ Utilization:")
        print(f"  Average: {cisq_avg:.2f}%")
        print(f"  Maximum: {cisq_max:.2f}%")
        print(f"  Minimum: {cisq_min:.2f}%")
        print(f"\nMISQ Utilization:")
        print(f"  Average: {misq_avg:.2f}%")
        print(f"  Maximum: {misq_max:.2f}%")
        print(f"  Minimum: {misq_min:.2f}%")
        
        # Check for full queue conditions
        cisq_full = (df['CISQ_Utilization'] >= 100).sum()
        misq_full = (df['MISQ_Utilization'] >= 100).sum()
        
        if cisq_full > 0:
            print(f"\nWarning: CISQ queue was full {cisq_full} times")
        if misq_full > 0:
            print(f"Warning: MISQ queue was full {misq_full} times")
        
        plt.show()
        return True
        
    except Exception as e:
        print(f"Error reading file: {e}")
        return False

def plot_combined(filename='isq_monitor.log'):
    """Plot both utilizations on same chart"""
    
    if not os.path.exists(filename):
        print(f"Error: File '{filename}' not found")
        return False
    
    try:
        df = pd.read_csv(filename)
        
        if df.empty:
            print("Warning: File is empty")
            return False
        
        plt.figure(figsize=(12, 6))
        
        # Plot both lines
        plt.plot(df['Time'], df['CISQ_Utilization'], 'b-', linewidth=2, 
                marker='o', markersize=2, label='CISQ Utilization', alpha=0.8)
        plt.plot(df['Time'], df['MISQ_Utilization'], 'r-', linewidth=2, 
                marker='s', markersize=2, label='MISQ Utilization', alpha=0.8)
        
        # Add reference lines
        plt.axhline(y=50, color='gray', linestyle='--', alpha=0.5, label='50%')
        plt.axhline(y=80, color='orange', linestyle='--', alpha=0.5, label='80%')
        plt.axhline(y=100, color='red', linestyle='--', alpha=0.5, label='100%')
        
        plt.xlabel('Simulation Time')
        plt.ylabel('Utilization (%)')
        plt.title('ISQ Utilization Comparison', fontweight='bold')
        plt.grid(True, alpha=0.3)
        plt.ylim(0, 100)
        plt.legend()
        plt.tight_layout()
        plt.show()
        return True
        
    except Exception as e:
        print(f"Error reading file: {e}")
        return False

def main():
    print("ISQ Utilization Plotter")
    print("=" * 30)
    
    # Get filename from command line or use default
    if len(sys.argv) > 1:
        filename = sys.argv[1]
    else:
        filename = 'isq_monitor.log'
    
    if not os.path.exists(filename):
        print(f"File not found: {filename}")
        filename = input("Enter ISQ monitor file path: ").strip()
        if not filename:
            print("No file specified, exiting")
            return
    
    # Ask user for plot type
    print("\nChoose plot type:")
    print("1. Separate plots (default)")
    print("2. Combined plot")
    
    choice = input("Enter choice (1/2): ").strip()
    
    if choice == '2':
        success = plot_combined(filename)
    else:
        success = plot_isq_data(filename)
    
    if success:
        print("\nPlot generated successfully!")
    else:
        print("\nFailed to generate plot")

if __name__ == "__main__":
    main()
