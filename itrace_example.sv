// 示例：如何在验证环境中实例化 itrace_monitor 模块
// 这个文件展示了如何在testbench中使用重构后的itrace模块

module npu_tb;

    // 时钟和复位信号
    logic clk;
    logic pe_resetn;
    
    // 其他testbench信号和逻辑
    // ...
    
    // 实例化 itrace_monitor 模块
    itrace_monitor u_itrace_monitor (
        .clk(clk),
        .pe_resetn(pe_resetn)
    );
    
    // 时钟生成
    initial begin
        clk = 0;
        forever #5 clk = ~clk;
    end
    
    // 复位序列
    initial begin
        pe_resetn = 0;
        #100;
        pe_resetn = 1;
    end
    
    // 其他testbench逻辑
    // ...

endmodule
