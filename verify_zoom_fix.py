#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证缩放功能修复的脚本
"""

import os
import webbrowser

def verify_javascript_syntax():
    """验证HTML文件中的JavaScript语法是否正确"""
    html_file = 'itrace_timeline.html'
    
    if not os.path.exists(html_file):
        print("❌ HTML文件不存在")
        return False
    
    print("🔍 检查JavaScript语法...")
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    # 检查是否还有双花括号语法错误
    syntax_checks = [
        ('双花括号语法错误', '{{', False),  # 不应该存在
        ('updateZoom函数', 'function updateZoom() {', True),  # 应该存在
        ('currentZoom变量', 'currentZoom = zoom;', True),
        ('calculateBarPosition函数', 'function calculateBarPosition(start, end) {', True),
        ('缩放因子应用', '* currentZoom', True),
        ('事件监听器', 'addEventListener(\'input\', updateZoom)', True),
    ]
    
    all_passed = True
    
    for check_name, pattern, should_exist in syntax_checks:
        found = pattern in content
        if should_exist and found:
            print(f"✅ {check_name}: 正确")
        elif not should_exist and not found:
            print(f"✅ {check_name}: 已修复")
        else:
            if should_exist:
                print(f"❌ {check_name}: 缺失 - {pattern}")
            else:
                print(f"❌ {check_name}: 仍然存在 - {pattern}")
            all_passed = False
    
    return all_passed

def check_zoom_implementation():
    """检查缩放功能的完整实现"""
    html_file = 'itrace_timeline.html'
    
    with open(html_file, 'r', encoding='utf-8') as f:
        content = f.read()
    
    print("\n🔧 检查缩放功能实现...")
    
    implementation_checks = [
        ('缩放滑块HTML', 'id="zoomSlider"'),
        ('缩放值显示', 'id="zoomValue"'),
        ('缩放范围设置', 'min="0.1" max="5"'),
        ('时间轴宽度更新', 'timeAxis.style.minWidth'),
        ('时间线容器宽度更新', 'bar.style.minWidth'),
        ('重新渲染调用', 'initTimeAxis()'),
        ('重新渲染指令', 'renderInstructions()'),
    ]
    
    all_passed = True
    
    for check_name, pattern in implementation_checks:
        if pattern in content:
            print(f"✅ {check_name}: 已实现")
        else:
            print(f"❌ {check_name}: 未找到")
            all_passed = False
    
    return all_passed

def main():
    """主函数"""
    print("🔧 验证缩放功能修复")
    print("="*50)
    
    # 检查JavaScript语法
    syntax_ok = verify_javascript_syntax()
    
    # 检查缩放功能实现
    implementation_ok = check_zoom_implementation()
    
    print("\n" + "="*50)
    
    if syntax_ok and implementation_ok:
        print("✅ 缩放功能修复验证通过!")
        print("\n🎯 修复内容总结:")
        print("• 修复了JavaScript双花括号语法错误")
        print("• 修复了calculateBarPosition函数的缩放计算")
        print("• 修复了updateZoom函数的重新渲染逻辑")
        print("• 修复了时间轴的缩放支持")
        
        print("\n📋 测试说明:")
        print("1. 在浏览器中打开HTML文件")
        print("2. 拖动缩放滑块（0.1x - 5.0x）")
        print("3. 观察缩放值是否实时更新")
        print("4. 观察时间线是否随缩放变化")
        print("5. 测试重置视图功能")
        
        # 打开HTML文件供测试
        html_file = 'itrace_timeline.html'
        try:
            file_path = f'file://{os.path.abspath(html_file)}'
            webbrowser.open(file_path)
            print(f"\n🌐 已在浏览器中打开: {file_path}")
        except Exception as e:
            print(f"\n⚠️  无法自动打开浏览器: {e}")
        
    else:
        print("❌ 缩放功能修复验证失败!")
        if not syntax_ok:
            print("• JavaScript语法仍有问题")
        if not implementation_ok:
            print("• 缩放功能实现不完整")

if __name__ == "__main__":
    main()
