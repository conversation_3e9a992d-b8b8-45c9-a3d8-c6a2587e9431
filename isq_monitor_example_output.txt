# ISQ Monitor 输出示例 - 每10拍采样，无dispatch信号

## 修改后的CSV格式

输出文件：isq_monitor.log

```csv
Time,CycleCount,CISQ_VLD_CNT,MISQ_VLD_CNT,CISQ_Utilization,MISQ_Utilization
100,10,5,3,15.63,9.38
200,20,8,6,25.00,18.75
300,30,12,9,37.50,28.13
400,40,15,12,46.88,37.50
500,50,18,15,56.25,46.88
600,60,22,18,68.75,56.25
700,70,25,21,78.13,65.63
800,80,28,24,87.50,75.00
900,90,30,27,93.75,84.38
1000,100,32,30,100.00,93.75
```

## 字段说明

- **Time**: 仿真时间（时间单位）
- **CycleCount**: 周期计数（每10拍递增10）
- **CISQ_VLD_CNT**: CISQ队列中有效条目数量
- **MISQ_VLD_CNT**: MISQ队列中有效条目数量  
- **CISQ_Utilization**: CISQ利用率百分比（当前计数/最大计数*100）
- **MISQ_Utilization**: MISQ利用率百分比（当前计数/最大计数*100）

## 控制台输出示例

```
ISQ Monitor (Include Version) initialized
Output file: isq_monitor.log
Dump interval: 10 cycles
Max count value: 32

Time: 100 - ISQ Status: CISQ=5/32(15.6%), MISQ=3/32(9.4%), Cycle=10
Time: 200 - ISQ Status: CISQ=8/32(25.0%), MISQ=6/32(18.8%), Cycle=20
Time: 300 - ISQ Status: CISQ=12/32(37.5%), MISQ=9/32(28.1%), Cycle=30
...
WARNING: CISQ is full (32/32) at time 1000
Time: 1000 - ISQ Status: CISQ=32/32(100.0%), MISQ=30/32(93.8%), Cycle=100

Final ISQ status dump at simulation end:
Time: 1050 - ISQ Status: CISQ=28/32(87.5%), MISQ=25/32(78.1%), Cycle=105

ISQ Monitor Summary:
  Total cycles monitored: 105
  Total dumps: 11
  Output file: isq_monitor.log
```

## 主要变化

### ✅ 采样频率提高
- **之前**: 每1000拍采样一次
- **现在**: 每10拍采样一次
- **优势**: 更高的时间分辨率，能捕获更细粒度的变化

### ✅ 简化输出格式
- **移除**: Dispatch_Port0, Dispatch_Port1 字段
- **保留**: 时间、周期、计数值、利用率
- **优势**: 更专注于队列状态监控

### ✅ 更快的响应
- 能够更快地检测到队列满的情况
- 更及时的性能瓶颈识别
- 更详细的利用率变化趋势

## 使用建议

### 数据分析
```python
import pandas as pd
import matplotlib.pyplot as plt

# 读取数据
df = pd.read_csv('isq_monitor.log')

# 绘制利用率趋势（每10拍一个数据点）
plt.figure(figsize=(12, 6))
plt.plot(df['CycleCount'], df['CISQ_Utilization'], 'b-', label='CISQ Utilization', linewidth=2)
plt.plot(df['CycleCount'], df['MISQ_Utilization'], 'r-', label='MISQ Utilization', linewidth=2)
plt.xlabel('Cycle Count')
plt.ylabel('Utilization (%)')
plt.title('ISQ Utilization Over Time (10-cycle sampling)')
plt.legend()
plt.grid(True, alpha=0.3)
plt.show()

# 统计分析
print(f"CISQ平均利用率: {df['CISQ_Utilization'].mean():.2f}%")
print(f"MISQ平均利用率: {df['MISQ_Utilization'].mean():.2f}%")
print(f"CISQ最大利用率: {df['CISQ_Utilization'].max():.2f}%")
print(f"MISQ最大利用率: {df['MISQ_Utilization'].max():.2f}%")
```

### 性能监控
- **实时监控**: 每10拍的高频采样适合实时性能监控
- **瓶颈识别**: 快速识别队列接近满的时刻
- **趋势分析**: 观察利用率的短期和长期变化趋势
