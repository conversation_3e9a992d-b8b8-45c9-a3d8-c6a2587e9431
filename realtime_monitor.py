#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Real-time ISQ Utilization Monitor
Display utilization changes in real-time during simulation

Usage:
python realtime_monitor.py [filename]
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.animation as animation
import os
import time
import argparse

class RealtimeISQMonitor:
    def __init__(self, filename='isq_monitor.log', update_interval=1000):
        """
        Initialize real-time monitor

        Args:
            filename: ISQ monitor output filename
            update_interval: Update interval (milliseconds)
        """
        self.filename = filename
        self.update_interval = update_interval
        self.last_size = 0
        self.data_buffer = []
        
        # 创建图表
        self.fig, (self.ax1, self.ax2) = plt.subplots(2, 1, figsize=(12, 8))
        self.setup_plots()
        
    def setup_plots(self):
        """Setup plot styles"""
        # CISQ chart
        self.ax1.set_ylabel('CISQ Utilization (%)', fontsize=11)
        self.ax1.set_title('Real-time ISQ Utilization Monitor', fontsize=14, fontweight='bold')
        self.ax1.grid(True, alpha=0.3)
        self.ax1.set_ylim(0, 100)

        # MISQ chart
        self.ax2.set_xlabel('Simulation Time', fontsize=11)
        self.ax2.set_ylabel('MISQ Utilization (%)', fontsize=11)
        self.ax2.grid(True, alpha=0.3)
        self.ax2.set_ylim(0, 100)

        # Initialize empty lines
        self.cisq_line, = self.ax1.plot([], [], 'b-', linewidth=2, label='CISQ')
        self.misq_line, = self.ax2.plot([], [], 'r-', linewidth=2, label='MISQ')

        # Add legends
        self.ax1.legend(loc='upper right')
        self.ax2.legend(loc='upper right')

        # Status text
        self.status_text = self.fig.suptitle('Waiting for data...', fontsize=12)
        
    def read_new_data(self):
        """读取新增的数据"""
        try:
            if not os.path.exists(self.filename):
                return []
            
            # 检查文件大小是否变化
            current_size = os.path.getsize(self.filename)
            if current_size <= self.last_size:
                return []
            
            # 读取整个文件
            df = pd.read_csv(self.filename)
            
            # 如果是第一次读取或文件被重写
            if len(df) < len(self.data_buffer):
                self.data_buffer = []
            
            # 获取新数据
            new_data = df.iloc[len(self.data_buffer):].copy()
            self.data_buffer.extend(new_data.to_dict('records'))
            self.last_size = current_size
            
            return new_data
            
        except Exception as e:
            print(f"读取数据时出错: {e}")
            return []
    
    def update_plots(self, frame):
        """更新图表"""
        new_data = self.read_new_data()
        
        if not self.data_buffer:
            return self.cisq_line, self.misq_line
        
        # 转换为DataFrame
        df = pd.DataFrame(self.data_buffer)
        
        # 更新数据
        times = df['Time'].values
        cisq_util = df['CISQ_Utilization'].values
        misq_util = df['MISQ_Utilization'].values
        
        # 更新线条
        self.cisq_line.set_data(times, cisq_util)
        self.misq_line.set_data(times, misq_util)
        
        # 自动调整X轴
        if len(times) > 0:
            # 显示最近的数据窗口
            window_size = min(1000, len(times))  # 显示最近1000个点
            if len(times) > window_size:
                start_idx = len(times) - window_size
                x_min, x_max = times[start_idx], times[-1]
            else:
                x_min, x_max = times[0], times[-1]
            
            margin = (x_max - x_min) * 0.05
            self.ax1.set_xlim(x_min - margin, x_max + margin)
            self.ax2.set_xlim(x_min - margin, x_max + margin)
        
        # 更新状态信息
        if len(df) > 0:
            current_time = df['Time'].iloc[-1]
            current_cycle = df['CycleCount'].iloc[-1]
            cisq_current = df['CISQ_Utilization'].iloc[-1]
            misq_current = df['MISQ_Utilization'].iloc[-1]
            
            status = f'时间: {current_time:,} | 周期: {current_cycle:,} | CISQ: {cisq_current:.1f}% | MISQ: {misq_current:.1f}% | 数据点: {len(df):,}'
            self.status_text.set_text(status)
            
            # 检查告警条件
            if cisq_current >= 95 or misq_current >= 95:
                self.status_text.set_color('red')
            elif cisq_current >= 80 or misq_current >= 80:
                self.status_text.set_color('orange')
            else:
                self.status_text.set_color('black')
        
        return self.cisq_line, self.misq_line
    
    def start_monitoring(self):
        """开始实时监控"""
        print(f"开始实时监控: {self.filename}")
        print(f"更新间隔: {self.update_interval}ms")
        print("关闭图表窗口或按Ctrl+C停止监控")
        
        # 创建动画
        ani = animation.FuncAnimation(
            self.fig, self.update_plots, 
            interval=self.update_interval,
            blit=False, cache_frame_data=False
        )
        
        plt.tight_layout()
        
        try:
            plt.show()
        except KeyboardInterrupt:
            print("\n监控已停止")
        
        return ani

def wait_for_file(filename, timeout=30):
    """等待文件出现"""
    print(f"等待文件 {filename} 出现...")
    start_time = time.time()
    
    while not os.path.exists(filename):
        if time.time() - start_time > timeout:
            print(f"超时: {timeout}秒内未找到文件")
            return False
        time.sleep(1)
        print(".", end="", flush=True)
    
    print(f"\n文件 {filename} 已找到!")
    return True

def main():
    parser = argparse.ArgumentParser(description='实时ISQ利用率监控器')
    parser.add_argument('filename', nargs='?', default='isq_monitor.log',
                       help='ISQ监控输出文件名 (默认: isq_monitor.log)')
    parser.add_argument('--interval', type=int, default=500,
                       help='更新间隔(毫秒) (默认: 500)')
    parser.add_argument('--wait', action='store_true',
                       help='等待文件出现')
    
    args = parser.parse_args()
    
    print("实时ISQ利用率监控器")
    print("=" * 40)
    
    # 检查文件是否存在
    if not os.path.exists(args.filename):
        if args.wait:
            if not wait_for_file(args.filename):
                return
        else:
            print(f"错误: 文件 {args.filename} 不存在")
            print("使用 --wait 参数等待文件出现，或确保ISQ监控器正在运行")
            return
    
    # 启动监控
    monitor = RealtimeISQMonitor(args.filename, args.interval)
    monitor.start_monitoring()

if __name__ == "__main__":
    main()
