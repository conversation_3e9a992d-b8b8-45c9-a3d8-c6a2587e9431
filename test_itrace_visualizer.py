#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版ITrace Timeline Visualizer测试脚本
"""

import os
import json

def create_test_data():
    """创建测试数据"""
    test_data = """InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1025,1030,1035,1040,1055
3,0x000000001010,0x00000000fb0012ab,"twait",1050,1055,0,0,0
4,0x000000001014,0x00000000fb801000,"ace_bsync x0",1060,1065,0,0,0
5,0x000000001018,0x00000000fb345678,"tcsrw.i 0x5",1070,1075,1080,1085,1090
6,0x000000001020,0x00000000fb456789,"tld.linear.u16 t6, (x11)",1095,1100,1105,1110,1125
7,0x000000001028,0x00000000fb567890,"tmma.tnt t4, t2, t3",1130,1135,1140,1145,1165
8,0x000000001030,0x00000000fb678901,"tst.linear.u32 (x12), t7",1170,1175,1180,1185,1195
"""
    
    with open('test_itrace.log', 'w', encoding='utf-8') as f:
        f.write(test_data)
    
    print("测试数据已生成: test_itrace.log")
    return 'test_itrace.log'

def parse_csv_simple(filename):
    """简单的CSV解析器"""
    instructions = []
    
    with open(filename, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 跳过标题行
    for i, line in enumerate(lines[1:], 1):
        if not line.strip():
            continue
            
        parts = line.strip().split(',')
        if len(parts) < 9:
            continue
        
        try:
            instr_id = int(parts[0])
            pc = parts[1]
            instruction = parts[2]
            disassembly = parts[3].strip('"')
            fetch_start = int(parts[4]) if parts[4] != '0' else 0
            decode_start = int(parts[5]) if parts[5] != '0' else 0
            dispatch_start = int(parts[6]) if parts[6] != '0' else 0
            execute_start = int(parts[7]) if parts[7] != '0' else 0
            execute_end = int(parts[8]) if parts[8] != '0' else 0
            
            # 处理各个执行阶段
            stages = []
            
            # Fetch阶段
            if fetch_start > 0:
                fetch_end = decode_start if decode_start > 0 else fetch_start + 1
                stages.append({
                    'name': 'fetch',
                    'start': fetch_start,
                    'end': fetch_end,
                    'duration': fetch_end - fetch_start,
                    'color': '#FF6B6B'
                })
            
            # Decode阶段
            if decode_start > 0:
                decode_end = dispatch_start if dispatch_start > 0 else decode_start + 1
                stages.append({
                    'name': 'decode',
                    'start': decode_start,
                    'end': decode_end,
                    'duration': decode_end - decode_start,
                    'color': '#4ECDC4'
                })
            
            # Dispatch阶段
            if dispatch_start > 0:
                dispatch_end = execute_start if execute_start > 0 else dispatch_start + 1
                stages.append({
                    'name': 'dispatch',
                    'start': dispatch_start,
                    'end': dispatch_end,
                    'duration': dispatch_end - dispatch_start,
                    'color': '#45B7D1'
                })
            
            # Execute阶段
            if execute_start > 0 and execute_end > 0:
                stages.append({
                    'name': 'execute',
                    'start': execute_start,
                    'end': execute_end,
                    'duration': execute_end - execute_start,
                    'color': '#96CEB4'
                })
            
            if stages:
                instr = {
                    'id': instr_id,
                    'pc': pc,
                    'instruction': instruction,
                    'disassembly': disassembly,
                    'stages': stages,
                    'total_start': min(stage['start'] for stage in stages),
                    'total_end': max(stage['end'] for stage in stages),
                    'total_duration': max(stage['end'] for stage in stages) - min(stage['start'] for stage in stages)
                }
                instructions.append(instr)
                
        except (ValueError, IndexError) as e:
            print(f"解析第{i}行时出错: {e}")
            continue
    
    return instructions

def generate_simple_html(instructions, output_filename='itrace_timeline.html'):
    """生成简化的HTML可视化"""
    
    if not instructions:
        print("没有有效的指令数据")
        return False
    
    # 计算时间范围
    all_times = []
    for instr in instructions:
        for stage in instr['stages']:
            all_times.extend([stage['start'], stage['end']])
    
    min_time = min(all_times)
    max_time = max(all_times)
    time_range = max_time - min_time
    
    # 生成指令行HTML
    instruction_rows = []
    for instr in instructions:
        stage_bars = []
        for stage in instr['stages']:
            start_percent = ((stage['start'] - min_time) / time_range) * 100
            width_percent = ((stage['end'] - stage['start']) / time_range) * 100
            width_percent = max(width_percent, 0.5)  # 最小宽度
            
            stage_bars.append(f"""
                <div class="stage-bar {stage['name']}" 
                     style="left: {start_percent:.2f}%; width: {width_percent:.2f}%; background-color: {stage['color']}; position: absolute; height: 12px; border-radius: 6px; display: flex; align-items: center; justify-content: center; font-size: 10px; font-weight: bold; color: white;">
                    {stage['name'] if width_percent > 2 else ''}
                </div>
            """)
        
        row_html = f"""
            <div style="display: flex; align-items: center; margin-bottom: 15px; padding: 10px; background: #fafafa; border-radius: 8px; border-left: 4px solid #007bff;">
                <div style="width: 300px; flex-shrink: 0; padding-right: 20px;">
                    <div style="font-weight: bold; color: #007bff; font-size: 1.1em;">指令 #{instr['id']}</div>
                    <div style="color: #666; font-family: monospace; font-size: 0.9em;">{instr['pc']}</div>
                    <div style="color: #333; font-family: monospace; font-size: 0.9em; margin-top: 5px;">{instr['disassembly']}</div>
                </div>
                <div style="flex: 1; position: relative; height: 60px; min-width: 800px;">
                    {''.join(stage_bars)}
                </div>
            </div>
        """
        instruction_rows.append(row_html)
    
    # HTML模板
    html_content = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>ITrace 指令执行时间线</title>
    <style>
        body {{
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }}
        
        .header {{
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            margin-bottom: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }}
        
        .header h1 {{
            margin: 0;
            font-size: 2.5em;
            text-align: center;
        }}
        
        .stats {{
            display: flex;
            justify-content: space-around;
            margin-top: 15px;
            font-size: 1.1em;
        }}
        
        .timeline-container {{
            background: white;
            border-radius: 10px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            overflow-x: auto;
        }}
        
        .legend {{
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            justify-content: center;
        }}
        
        .legend-item {{
            display: flex;
            align-items: center;
            gap: 8px;
            font-weight: 500;
        }}
        
        .legend-color {{
            width: 20px;
            height: 20px;
            border-radius: 4px;
        }}
        
        .stage-bar.fetch {{ top: 5px; }}
        .stage-bar.decode {{ top: 20px; }}
        .stage-bar.dispatch {{ top: 35px; }}
        .stage-bar.execute {{ top: 50px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>ITrace 指令执行时间线可视化</h1>
        <div class="stats">
            <div>总指令数: <strong>{len(instructions)}</strong></div>
            <div>时间范围: <strong>{min_time} - {max_time}</strong></div>
            <div>总执行时间: <strong>{time_range}</strong></div>
        </div>
    </div>
    
    <div class="timeline-container">
        <div class="legend">
            <div class="legend-item">
                <div class="legend-color" style="background: #FF6B6B;"></div>
                <span>Fetch</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #4ECDC4;"></div>
                <span>Decode</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #45B7D1;"></div>
                <span>Dispatch</span>
            </div>
            <div class="legend-item">
                <div class="legend-color" style="background: #96CEB4;"></div>
                <span>Execute</span>
            </div>
        </div>
        
        <div>
            {''.join(instruction_rows)}
        </div>
    </div>
</body>
</html>
    """
    
    # 写入文件
    try:
        with open(output_filename, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"HTML可视化文件已生成: {output_filename}")
        print(f"包含 {len(instructions)} 条指令的时间线")
        print(f"时间范围: {min_time} - {max_time}")
        return True
        
    except Exception as e:
        print(f"写入HTML文件时出错: {e}")
        return False

def main():
    print("=" * 60)
    print("ITrace Timeline Visualizer (简化测试版)")
    print("=" * 60)
    
    # 创建测试数据
    test_file = create_test_data()
    
    # 解析数据
    print(f"正在解析文件: {test_file}")
    instructions = parse_csv_simple(test_file)
    
    if not instructions:
        print("没有找到有效的指令数据")
        return
    
    print(f"成功解析 {len(instructions)} 条指令")
    
    # 生成HTML
    success = generate_simple_html(instructions)
    
    if success:
        print("-" * 60)
        print("✅ 可视化生成成功!")
        print(f"📁 输出文件: {os.path.abspath('itrace_timeline.html')}")
        print("💡 请在浏览器中打开 itrace_timeline.html 查看结果")
    else:
        print("❌ 可视化生成失败")

if __name__ == "__main__":
    main()
