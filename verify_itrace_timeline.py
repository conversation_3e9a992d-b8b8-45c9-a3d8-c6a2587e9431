#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ITrace Timeline Visualizer 验证脚本
验证主要功能是否正常工作
"""

import os
import sys

def test_basic_functionality():
    """测试基本功能"""
    print("=" * 60)
    print("ITrace Timeline Visualizer 功能验证")
    print("=" * 60)
    
    # 测试1: 检查文件是否存在
    print("测试1: 检查必要文件...")
    required_files = [
        'itrace_timeline_visualizer.py',
        'test_itrace_visualizer.py', 
        'test_itrace.log',
        'itrace_timeline.html',
        'README_ITrace_Timeline.md',
        'run_itrace_timeline.bat'
    ]
    
    missing_files = []
    for file in required_files:
        if os.path.exists(file):
            print(f"  ✅ {file}")
        else:
            print(f"  ❌ {file}")
            missing_files.append(file)
    
    if missing_files:
        print(f"\n警告: 缺少文件: {missing_files}")
    else:
        print("\n✅ 所有必要文件都存在")
    
    # 测试2: 检查测试数据格式
    print("\n测试2: 检查测试数据格式...")
    try:
        with open('test_itrace.log', 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        if len(lines) >= 2:  # 至少有标题行和一行数据
            header = lines[0].strip()
            expected_columns = ['InstrID', 'PC', 'Instruction', 'Disassembly', 
                              'FetchStartTime', 'DecodeStartTime', 'DispatchStartTime', 
                              'ExecuteStartTime', 'ExecuteEndTime']
            
            header_columns = header.split(',')
            if all(col in header_columns for col in expected_columns):
                print("  ✅ CSV格式正确")
                print(f"  ✅ 包含 {len(lines)-1} 行数据")
            else:
                print("  ❌ CSV格式不正确")
                print(f"  期望列: {expected_columns}")
                print(f"  实际列: {header_columns}")
        else:
            print("  ❌ 数据文件为空或格式不正确")
            
    except Exception as e:
        print(f"  ❌ 读取测试数据时出错: {e}")
    
    # 测试3: 检查HTML文件
    print("\n测试3: 检查HTML文件...")
    try:
        with open('itrace_timeline.html', 'r', encoding='utf-8') as f:
            html_content = f.read()
        
        # 检查关键元素
        key_elements = [
            'ITrace 指令执行时间线',
            'timeline-container',
            'instruction-row',
            'stage-bar',
            'const instructions',
            'renderInstructions'
        ]
        
        missing_elements = []
        for element in key_elements:
            if element in html_content:
                print(f"  ✅ 包含 {element}")
            else:
                print(f"  ❌ 缺少 {element}")
                missing_elements.append(element)
        
        if not missing_elements:
            print("  ✅ HTML文件结构完整")
        else:
            print(f"  ⚠️  缺少元素: {missing_elements}")
            
    except Exception as e:
        print(f"  ❌ 读取HTML文件时出错: {e}")
    
    # 测试4: 模拟数据解析
    print("\n测试4: 模拟数据解析...")
    try:
        # 简单的CSV解析测试
        test_line = '1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020'
        parts = test_line.split(',')
        
        if len(parts) >= 9:
            instr_id = int(parts[0])
            pc = parts[1]
            fetch_start = int(parts[4])
            execute_end = int(parts[8])
            
            print(f"  ✅ 解析指令ID: {instr_id}")
            print(f"  ✅ 解析PC地址: {pc}")
            print(f"  ✅ 解析时间范围: {fetch_start} - {execute_end}")
            print("  ✅ 数据解析功能正常")
        else:
            print("  ❌ 数据解析失败")
            
    except Exception as e:
        print(f"  ❌ 数据解析测试出错: {e}")
    
    # 测试5: 检查Python导入
    print("\n测试5: 检查Python依赖...")
    try:
        import json
        print("  ✅ json模块可用")
        
        import os
        print("  ✅ os模块可用")
        
        import sys
        print("  ✅ sys模块可用")
        
        try:
            import pandas
            print("  ✅ pandas模块可用")
        except ImportError:
            print("  ⚠️  pandas模块未安装（可选）")
        
        print("  ✅ Python环境检查完成")
        
    except Exception as e:
        print(f"  ❌ Python环境检查出错: {e}")
    
    # 总结
    print("\n" + "=" * 60)
    print("验证完成！")
    print("=" * 60)
    
    print("\n使用建议:")
    print("1. 如果所有测试都通过，可以直接使用 run_itrace_timeline.bat")
    print("2. 如果缺少pandas，运行: pip install pandas")
    print("3. 查看 README_ITrace_Timeline.md 了解详细使用方法")
    print("4. 在浏览器中打开 itrace_timeline.html 查看示例效果")

def create_sample_data():
    """创建示例数据用于测试"""
    print("\n创建示例数据...")
    
    sample_data = """InstrID,PC,Instruction,Disassembly,FetchStartTime,DecodeStartTime,DispatchStartTime,ExecuteStartTime,ExecuteEndTime
1,0x000000001000,0x00000000fb123456,"tld.trii.linear.u32.global t5, (x10)",1000,1005,1010,1015,1020
2,0x000000001008,0x00000000fb234567,"tmma.ttt t3, t1, t2",1025,1030,1035,1040,1055
3,0x000000001010,0x00000000fb0012ab,"twait",1050,1055,0,0,0
4,0x000000001014,0x00000000fb801000,"ace_bsync x0",1060,1065,0,0,0
5,0x000000001018,0x00000000fb345678,"tcsrw.i 0x5",1070,1075,1080,1085,1090
6,0x000000001020,0x00000000fb456789,"tld.linear.u16 t6, (x11)",1095,1100,1105,1110,1125
7,0x000000001028,0x00000000fb567890,"tmma.tnt t4, t2, t3",1130,1135,1140,1145,1165
8,0x000000001030,0x00000000fb678901,"tst.linear.u32 (x12), t7",1170,1175,1180,1185,1195
"""
    
    try:
        with open('sample_itrace.log', 'w', encoding='utf-8') as f:
            f.write(sample_data)
        print("✅ 示例数据已创建: sample_itrace.log")
        return True
    except Exception as e:
        print(f"❌ 创建示例数据失败: {e}")
        return False

def main():
    """主函数"""
    test_basic_functionality()
    
    # 询问是否创建示例数据
    print("\n" + "-" * 40)
    create_sample = input("是否创建新的示例数据文件? (y/n): ").lower().strip()
    if create_sample in ['y', 'yes', '是']:
        create_sample_data()
    
    print("\n验证脚本执行完成！")

if __name__ == "__main__":
    main()
