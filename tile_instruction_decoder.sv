// Tile Extension ISA Instruction Decoder
// SystemVerilog implementation for instruction length detection, 
// tile instruction identification, and disassembly

package tile_decoder_pkg;

    // Instruction length enumeration
    typedef enum logic [1:0] {
        INSTR_32BIT  = 2'b00,
        INSTR_64BIT  = 2'b01,
        INSTR_96BIT  = 2'b10,
        INSTR_128BIT = 2'b11
    } instr_length_e;

    // ACE_OP constant for tile instructions
    parameter logic [6:0] TILE_ACE_OP = 7'b1111011;

    // Instruction collection state
    typedef struct packed {
        logic [127:0] instruction_data;
        logic [2:0]   collected_words;  // Changed from [1:0] to [2:0] to support up to 4 words (128-bit instructions)
        instr_length_e expected_length;
        logic         is_complete;
        logic         is_tile_instr;
    } instr_collector_t;

    // Function to extract ACE_OP from 32-bit word
    function automatic logic [6:0] extract_ace_op(input logic [31:0] word);
        return word[6:0];
    endfunction

    // Function to check if instruction is a tile instruction
    function automatic logic is_tile_instruction(input logic [31:0] first_word);
        logic [6:0] ace_op;
        ace_op = extract_ace_op(first_word);
        return (ace_op == TILE_ACE_OP);
    endfunction

    // Function to check if instruction is a sync or wait instruction
    function automatic logic is_sync_or_wait_instruction(input logic [31:0] first_word);
        logic [6:0] ace_op;
        logic [2:0] tuop;
        logic [2:0] ctrluop;
        logic [2:0] waitop;
        logic [2:0] miscop;
        logic ace_misc_en;

        ace_op = extract_ace_op(first_word);

        // Check if this is a tile instruction
        if (ace_op != TILE_ACE_OP) begin
            return 1'b0; // Not a tile instruction
        end

        tuop = first_word[14:12];

        case (tuop)
            3'b101: begin // tuop_101 - sync operations
                ctrluop = first_word[25:23];
                waitop = first_word[30:28];

                // Check for twait instructions
                if (ctrluop == 3'b001 && waitop == 3'b111) begin
                    return 1'b1; // This is a twait instruction
                end

                // Check for other sync operations like tsync
                if (ctrluop == 3'b000) begin
                    return 1'b1; // This is a tsync instruction
                end

                return 1'b0;
            end

            3'b111: begin // tuop_111 - ACE operations
                miscop = first_word[28:26];
                ace_misc_en = first_word[31];

                if (ace_misc_en == 1'b1) begin
                    case (miscop)
                        3'b000: return 1'b1;  // ace_bsync
                        3'b010: return 1'b1;  // ace_nbsync
                        default: return 1'b0;
                    endcase
                end else begin
                    return 1'b0;
                end
            end

            default: return 1'b0; // Not a sync/wait instruction
        endcase
    endfunction

    // Function to determine instruction length based on first 32-bit word
    function automatic instr_length_e get_instruction_length(input logic [31:0] first_word);
        logic [6:0] ace_op;
        logic [2:0] tuop;
        logic [5:0] memuop;
        logic [1:0] lsuop;

        ace_op = extract_ace_op(first_word);

        // Check if this is a tile instruction
        if (ace_op != TILE_ACE_OP) begin
            return INSTR_32BIT; // Non-tile instruction, assume 32-bit
        end

        // Extract tuop field (bits [14:12])
        tuop = first_word[14:12];

        case (tuop)
            3'b000: begin // tuop_000 - memory operations
                memuop = first_word[30:25]; // Extract memuop field (bits 30:25)
                lsuop = first_word[11:10];  // Extract lsuop field

                case (lsuop)
                    2'b00: return INSTR_64BIT;  // Linear/stride operations
                    2'b01: return INSTR_64BIT;  // Other 64-bit operations
                    2'b10: return INSTR_96BIT;  // Index operations (3-lane)
                    2'b11: begin
                        // Check memuop for 128-bit operations
                        if (memuop == 6'b011100) // tacp operations
                            return INSTR_128BIT;
                        else
                            return INSTR_64BIT;
                    end
                endcase
            end

            3'b001: return INSTR_64BIT;  // tuop_001 - matrix operations
            3'b010: return INSTR_64BIT;  // tuop_010 - vector operations
            3'b011: return INSTR_64BIT;  // tuop_011 - other operations
            3'b100: return INSTR_32BIT;  // tuop_100 - CSR operations
            3'b101: return INSTR_32BIT;  // tuop_101 - sync operations
            3'b110: begin // tuop_110 - can be part of multi-lane instructions
                lsuop = first_word[11:10];  // Extract lsuop field
                memuop = first_word[30:25]; // Extract memuop field (bits 30:25)

                // Check for different instruction types
                if (memuop == 6'b000001) begin
                    // Block memory operations (dual-lane 64-bit)
                    return INSTR_64BIT;
                end else if (memuop == 6'b000000) begin
                    // Standard memory operations
                    case (lsuop)
                        2'b00: return INSTR_64BIT;  // Linear operations (dual-lane)
                        2'b01: return INSTR_64BIT;  // Stride operations (dual-lane)
                        2'b10: return INSTR_96BIT;  // Index operations (3-lane)
                        2'b11: return INSTR_64BIT;  // Other 64-bit operations
                    endcase
                end else if (memuop == 6'b011100) begin
                    // Tile copy operations (4-lane 128-bit)
                    return INSTR_128BIT;
                end else begin
                    // For other memuop values, use conservative approach:
                    // Most tuop_110 instructions are 64-bit, only a few specific
                    // patterns are 32-bit ACE instructions
                    // TODO: Add more specific detection for 32-bit ACE patterns
                    return INSTR_64BIT;  // Conservative: assume 64-bit for unknown patterns
                end
            end
            3'b111: begin // tuop_111 - can be part of multi-lane instructions
                memuop = first_word[30:25];

                // tuop_111 appears in lanes 2+ of multi-lane instructions
                // For now, assume these are part of larger instructions
                // The actual length will be determined by the first lane
                return INSTR_32BIT; // This shouldn't be called for lane 2+
            end

            default: return INSTR_32BIT;
        endcase
    endfunction

    // Function to initialize instruction collector
    function automatic instr_collector_t init_collector(input logic [31:0] first_word);
        instr_collector_t collector;

        collector.instruction_data = 128'h0;
        collector.instruction_data[31:0] = first_word;
        collector.collected_words = 3'b001; // First word collected
        collector.expected_length = get_instruction_length(first_word);
        collector.is_tile_instr = is_tile_instruction(first_word);

        // Check if instruction is complete (32-bit instructions)
        collector.is_complete = (collector.expected_length == INSTR_32BIT);

        return collector;
    endfunction

    // Function to add word to collector
    function automatic instr_collector_t add_word_to_collector(
        input instr_collector_t collector,
        input logic [31:0] word
    );
        instr_collector_t new_collector;

        new_collector = collector;

        if (!collector.is_complete && collector.collected_words < 4) begin
            case (collector.collected_words)
                3'b001: new_collector.instruction_data[63:32] = word;
                3'b010: new_collector.instruction_data[95:64] = word;
                3'b011: new_collector.instruction_data[127:96] = word;
            endcase

            new_collector.collected_words = collector.collected_words + 1;

            // Check if instruction is now complete
            case (collector.expected_length)
                INSTR_64BIT:  new_collector.is_complete = (new_collector.collected_words >= 2);
                INSTR_96BIT:  new_collector.is_complete = (new_collector.collected_words >= 3);
                INSTR_128BIT: new_collector.is_complete = (new_collector.collected_words >= 4);
                default:      new_collector.is_complete = 1'b1;
            endcase
        end

        return new_collector;
    endfunction

    // Function to get instruction bit width
    function automatic int get_instruction_bits(input instr_length_e length);
        case (length)
            INSTR_32BIT:  return 32;
            INSTR_64BIT:  return 64;
            INSTR_96BIT:  return 96;
            INSTR_128BIT: return 128;
            default:      return 32;
        endcase
    endfunction

    // Function to extract instruction fields for disassembly
    function automatic string extract_instruction_name(
        input logic [127:0] instruction_data,
        input instr_length_e length
    );
        logic [6:0] ace_op;
        logic [2:0] tuop;
        logic [5:0] memuop;
        logic [1:0] lsuop;
        logic [2:0] miscop;
        logic offseten, rmten;

        ace_op = instruction_data[6:0];

        if (ace_op != TILE_ACE_OP) begin
            return "unknown_non_tile";
        end

        tuop = instruction_data[14:12];

        case (tuop)
            3'b000: begin // Memory operations (tuop_000)
                memuop = instruction_data[30:25]; // Extract memuop field (bits 30:25)
                lsuop = instruction_data[11:10];

                case (memuop)
                    6'b000000: begin // Load operations
                        case (lsuop)
                            2'b00: begin // Linear operations (64-bit)
                                if (length == INSTR_64BIT) begin
                                    offseten = instruction_data[32+15];
                                    rmten = instruction_data[32+20];
                                    if (!offseten && !rmten)
                                        return "tld.trii.linear.u32.global";
                                    else if (!offseten && rmten)
                                        return "tld.trii.linear.u32.global.remote";
                                    else if (offseten && !rmten)
                                        return "tld.trir.linear.u32.global";
                                    else
                                        return "tld.trir.linear.u32.global.remote";
                                end
                            end
                            2'b01: return "tld.trri.stride.u32.global"; // Stride operations (64-bit)
                            2'b10: begin // Index operations (96-bit)
                                if (length == INSTR_96BIT) begin
                                    // Check offseten in the third lane (bits 64+15)
                                    offseten = instruction_data[64+15];
                                    if (!offseten)
                                        return "tld.trvi.asp.index.u32.global";
                                    else
                                        return "tld.trvr.asp.index.u32.global";
                                end
                            end
                        endcase
                    end
                    6'b001000: return "tst.trvi.index.u32.global"; // Store operations
                    6'b011100: begin // Tile copy operations (128-bit)
                        if (length == INSTR_128BIT) begin
                            // Check srctm and dsttm fields in the second lane
                            logic srctm, dsttm;
                            srctm = instruction_data[32+41]; // bit 41 in second lane
                            dsttm = instruction_data[32+42]; // bit 42 in second lane
                            if (!srctm && dsttm)
                                return "tacp.rvv.asp2.dsttm";
                            else if (srctm && !dsttm)
                                return "tacp.vvr.asp2.srctm";
                            else
                                return "tacp.rvrv.asp2.mbar.dsttm";
                        end
                    end
                endcase
            end

            3'b001: return "tmma.ttt"; // Matrix multiply (tuop_001)

            3'b110: begin // tuop_110 - multi-lane memory instructions
                // Simplified handling - just return generic names based on length
                case (length)
                    INSTR_64BIT: return "tld_64bit";   // Generic 64-bit memory operation
                    INSTR_96BIT: return "tld_96bit";   // Generic 96-bit memory operation
                    INSTR_128BIT: return "tacp_128bit"; // Generic 128-bit tile copy
                    default: return "unknown_tuop110";
                endcase
            end



            3'b100: begin // CSR operations
                logic [1:0] rw = instruction_data[31:30];
                case (rw)
                    2'b00: return "tcsrw.i";
                    2'b01: return "tcsrr.r";
                    2'b10: return "tcsrw.r";
                    default: return "unknown_csr";
                endcase
            end

            3'b101: begin // Sync operations (tuop_101)
                logic [2:0] ctrluop;
                logic [2:0] waitop;

                ctrluop = instruction_data[25:23];
                waitop = instruction_data[30:28];

                if (ctrluop == 3'b001 && waitop == 3'b111) begin
                    // Basic twait instruction
                    if (instruction_data[26] == 1'b1)
                        return "twait.mem";
                    else
                        return "twait";
                end else begin
                    return "unknown_sync";
                end
            end

            3'b111: begin // ACE operations (tuop_111)
                logic [2:0] miscop = instruction_data[28:26];
                logic ace_misc_en = instruction_data[31];

                if (ace_misc_en == 1'b1) begin
                    case (miscop)
                        3'b000: return "ace_bsync";  // ace_bsync
                        3'b010: return "ace_nbsync"; // ace_nbsync
                        default: return "unknown_ace_misc";
                    endcase
                end else begin
                    return "unknown_ace";
                end
            end

            default: return "unknown_tile";
        endcase

        return "unknown";
    endfunction

    // Function to format operands
    function automatic string format_operands(
        input logic [127:0] instruction_data,
        input instr_length_e length,
        input string instr_name
    );
        logic [7:0] td;
        logic [4:0] rs1, rs2;
        string operands = "";

        // Extract fields based on instruction type and length
        case (length)
            INSTR_32BIT: begin
                // 32-bit instructions (CSR, sync, ACE)
                if (instr_name.substr(0, 4) == "tcsr") begin
                    // CSR instructions have different layout
                    // Fields are in the single 32-bit word
                end
            end

            INSTR_64BIT: begin
                // Check for block memory instructions by examining the actual instruction fields
                // instead of relying on string matching which might be unreliable
                logic [2:0] tuop_first = instruction_data[14:12];
                logic [5:0] memuop_field = instruction_data[30:25];
                logic [2:0] tuop_second = instruction_data[32+14:32+12];

                if (tuop_first == 3'b110 && memuop_field == 6'b000001 && tuop_second == 3'b001) begin
                    // This is definitely a tld.trr.blk.* instruction based on field values
                    // Use the corrected field positions for block memory instructions
                    td = instruction_data[32+30:32+23];  // Td field in second word bits [30:23]
                    rs1 = instruction_data[32+19:32+15]; // rs1 field in second word bits [19:15]
                    rs2 = instruction_data[24:20];       // rs3 field in first word bits [24:20]
                end else if (instr_name.substr(0, 3) == "tld" && instr_name.substr(4, 3) == "trr") begin
                    // Fallback: string-based detection for other trr instructions
                    td = instruction_data[32+30:32+23];  // Td field in second word bits [30:23]
                    rs1 = instruction_data[32+19:32+15]; // rs1 field in second word bits [19:15]
                    rs2 = instruction_data[24:20];       // rs3 field in first word bits [24:20]
                end else begin
                    // Standard 64-bit layout for other instructions
                    td = instruction_data[32+7:32+0];    // Td field in second word
                    rs1 = instruction_data[32+20:32+16]; // rs1 field in second word
                end
            end

            INSTR_96BIT: begin
                // 3-lane instructions (index operations)
                td = instruction_data[32+23:32+16];  // Td field in second word bits [23:16]
                rs1 = instruction_data[32+15:32+11]; // rs1 field in second word bits [15:11]
            end

            INSTR_128BIT: begin
                // 4-lane instructions (tile copy operations)
                // Fields are distributed across multiple lanes
                rs1 = instruction_data[32+19:32+15]; // rs1 field in second word
            end
        endcase

        // Format based on instruction type and length
        case (length)
            INSTR_32BIT: begin
                if (instr_name.substr(0, 4) == "tcsr") begin
                    // CSR instructions have different formats based on rw field
                    logic [1:0] rw = instruction_data[31:30];
                    logic [8:0] csr_addr = instruction_data[28:20]; // 9-bit CSR address
                    logic [4:0] rd = instruction_data[11:7];
                    logic [4:0] rs1_or_imm = instruction_data[19:15];

                    case (rw)
                        2'b00: begin // tcsrw.i - write immediate
                            $sformat(operands, "0x%0x", rs1_or_imm); // Only immediate value
                        end
                        2'b01: begin // tcsrr.r - read to register
                            $sformat(operands, "x%0d", rd); // Only destination register
                        end
                        2'b10: begin // tcsrw.r - write from register
                            $sformat(operands, "x%0d", rs1_or_imm); // Only source register
                        end
                        default: operands = "unknown";
                    endcase
                end else if (instr_name == "twait" || instr_name == "twait.mem") begin
                    // Basic twait instructions - no operands
                    operands = "";
                end else if (instr_name == "twait.i.load.global" || instr_name == "twait.i.load.share" ||
                           instr_name == "twait.i.store.global" || instr_name == "twait.i.store.share") begin
                    // twait.i.* instructions - immediate operand
                    logic [7:0] cnt = instruction_data[22:15]; // cnt field bits [22:15]
                    $sformat(operands, "%0d", cnt);
                end else if (instr_name == "twait.r.load.global" || instr_name == "twait.r.load.share" ||
                           instr_name == "twait.r.store.global" || instr_name == "twait.r.store.share" ||
                           instr_name == "twait.r.tacp_cg" || instr_name == "twait.r.rmtfence") begin
                    // twait.r.* instructions - register operand
                    logic [4:0] rs1 = instruction_data[19:15]; // rs1 field bits [19:15]
                    $sformat(operands, "x%0d", rs1);
                end else if (instr_name == "tsync.i") begin
                    // tsync instructions
                    logic [4:0] sync_id = instruction_data[19:15]; // sync_id field bits [19:15]
                    $sformat(operands, "%0d", sync_id);
                end else if (instr_name == "tkill.r") begin
                    // tkill instructions
                    logic [4:0] rs1 = instruction_data[19:15]; // rs1 field bits [19:15]
                    $sformat(operands, "x%0d", rs1);
                end else if (instr_name == "ace_bsync" || instr_name == "ace_nbsync") begin
                    // ACE sync instructions: ace_bsync/ace_nbsync sync_id
                    logic [4:0] sync_id = instruction_data[19:15]; // sync_id field bits [19:15]
                    $sformat(operands, "x%0d", sync_id);
                end else begin
                    // Other 32-bit instructions
                    operands = "0";
                end
            end

            INSTR_64BIT: begin
                // Simplified 64-bit instructions: generic format
                $sformat(operands, "t%0d, (x%0d)", td, rs1);
            end

            INSTR_96BIT: begin
                // Simplified 96-bit instructions: generic format
                $sformat(operands, "t%0d, (x%0d)", td, rs1);
            end

            INSTR_128BIT: begin
                // Simplified 128-bit instructions: generic format
                $sformat(operands, "t%0d, t%0d, x%0d", td, td, rs1);
            end
        endcase

        return operands;
    endfunction

    // Main disassembly function
    function automatic string disassemble_instruction(
        input logic [127:0] instruction_data,
        input instr_length_e length
    );
        string instr_name, operands, result;

        instr_name = extract_instruction_name(instruction_data, length);
        operands = format_operands(instruction_data, length, instr_name);

        if (operands != "")
            $sformat(result, "%s %s", instr_name, operands);
        else
            result = instr_name;

        return result;
    endfunction

endpackage

import tile_decoder_pkg::*;

// Example module showing how to use the decoder functions
module tile_instruction_decoder_example;

    // Example usage in an initial block
    initial begin
        logic [31:0] test_word;
        instr_collector_t collector;
        string disasm_result;

        $display("=== Tile Instruction Decoder Example ===");

        // Test 32-bit instruction
        test_word = 32'h0000407b;
        $display("\nTesting 32-bit instruction: 0x%08x", test_word);

        if (tile_decoder_pkg::is_tile_instruction(test_word)) begin
            collector = tile_decoder_pkg::init_collector(test_word);
            $display("  Is tile: YES, Length: %0d, Complete: %s",
                    collector.expected_length,
                    collector.is_complete ? "YES" : "NO");

            if (collector.is_complete) begin
                disasm_result = tile_decoder_pkg::disassemble_instruction(
                    collector.instruction_data, collector.expected_length);
                $display("  Disassembly: %s", disasm_result);
            end
        end

        // Test 64-bit instruction
        $display("\nTesting 64-bit instruction:");
        test_word = 32'h0000607b;
        $display("  Word 1: 0x%08x", test_word);

        collector = tile_decoder_pkg::init_collector(test_word);
        $display("  Expected length: %0d, Complete: %s",
                collector.expected_length,
                collector.is_complete ? "YES" : "NO");

        if (!collector.is_complete) begin
            test_word = 32'h8000007b;
            $display("  Word 2: 0x%08x", test_word);
            collector = tile_decoder_pkg::add_word_to_collector(collector, test_word);
            $display("  Complete: %s", collector.is_complete ? "YES" : "NO");

            if (collector.is_complete) begin
                disasm_result = tile_decoder_pkg::disassemble_instruction(
                    collector.instruction_data, collector.expected_length);
                $display("  Disassembly: %s", disasm_result);
            end
        end

        $display("\n=== Example Complete ===");
    end

endmodule
